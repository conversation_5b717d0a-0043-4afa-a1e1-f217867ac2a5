"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Calendar, MapPin, ExternalLink } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const experiences = [
  {
    title: "Senior Full Stack Developer",
    company: "TechCorp Solutions",
    location: "Mumbai, India",
    period: "2022 - Present",
    type: "Full-time",
    description: "Leading development of scalable web applications using React, Next.js, and Node.js. Mentoring junior developers and implementing best practices for code quality and performance.",
    achievements: [
      "Increased application performance by 40% through optimization",
      "Led a team of 5 developers on multiple projects",
      "Implemented CI/CD pipelines reducing deployment time by 60%",
      "Architected microservices handling 1M+ requests daily"
    ],
    technologies: ["React", "Next.js", "TypeScript", "Node.js", "PostgreSQL", "AWS"],
    website: "https://techcorp.com"
  },
  {
    title: "Full Stack Developer",
    company: "StartupXYZ",
    location: "Remote",
    period: "2021 - 2022",
    type: "Full-time",
    description: "Developed and maintained multiple client projects using modern web technologies. Collaborated with design teams to create pixel-perfect user interfaces.",
    achievements: [
      "Built 10+ responsive web applications from scratch",
      "Reduced page load times by 50% through optimization",
      "Implemented real-time features using WebSocket",
      "Mentored 3 junior developers"
    ],
    technologies: ["React", "Vue.js", "Express.js", "MongoDB", "Firebase"],
    website: "https://startupxyz.com"
  },
  {
    title: "Frontend Developer",
    company: "Digital Agency Pro",
    location: "Mumbai, India",
    period: "2020 - 2021",
    type: "Full-time",
    description: "Focused on creating beautiful and functional user interfaces for various client projects. Worked closely with designers to bring mockups to life.",
    achievements: [
      "Delivered 20+ client projects on time and within budget",
      "Improved client satisfaction scores by 25%",
      "Implemented responsive designs for mobile-first approach",
      "Created reusable component library"
    ],
    technologies: ["HTML", "CSS", "JavaScript", "React", "Sass", "Figma"],
    website: "https://digitalagencypro.com"
  },
  {
    title: "Junior Web Developer",
    company: "WebDev Studio",
    location: "Mumbai, India",
    period: "2019 - 2020",
    type: "Full-time",
    description: "Started my professional journey learning modern web development practices and contributing to various client projects.",
    achievements: [
      "Completed 15+ small to medium-sized projects",
      "Learned modern JavaScript frameworks and tools",
      "Contributed to team's coding standards documentation",
      "Achieved 95% client satisfaction rating"
    ],
    technologies: ["HTML", "CSS", "JavaScript", "jQuery", "Bootstrap", "PHP"],
    website: "https://webdevstudio.com"
  }
];

export function Experience() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section ref={ref} className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Work Experience</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            My professional journey in web development, from junior developer to senior full-stack engineer.
          </p>
        </motion.div>

        <div className="space-y-8">
          {experiences.map((experience, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: index * 0.1 }}
            >
              <Card className="hover-lift border-border/50 hover:border-border transition-all duration-300">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                      <CardTitle className="text-xl font-bold">{experience.title}</CardTitle>
                      <div className="flex items-center space-x-2 text-muted-foreground mt-1">
                        <span className="font-medium text-primary">{experience.company}</span>
                        {experience.website && (
                          <a 
                            href={experience.website} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="hover:text-primary transition-colors"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{experience.period}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-4 w-4" />
                        <span>{experience.location}</span>
                      </div>
                      <Badge variant="outline">{experience.type}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{experience.description}</p>
                  
                  <div className="mb-4">
                    <h4 className="font-semibold mb-2">Key Achievements:</h4>
                    <ul className="space-y-1">
                      {experience.achievements.map((achievement, achievementIndex) => (
                        <li key={achievementIndex} className="flex items-start space-x-2 text-sm text-muted-foreground">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                          <span>{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Technologies:</h4>
                    <div className="flex flex-wrap gap-2">
                      {experience.technologies.map((tech) => (
                        <Badge key={tech} variant="secondary" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
