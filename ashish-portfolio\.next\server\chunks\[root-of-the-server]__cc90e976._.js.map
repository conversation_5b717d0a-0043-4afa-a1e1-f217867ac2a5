{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/app/robots.ts"], "sourcesContent": ["import { MetadataRoute } from 'next'\n\nexport default function robots(): MetadataRoute.Robots {\n  return {\n    rules: {\n      userAgent: '*',\n      allow: '/',\n      disallow: ['/private/', '/admin/'],\n    },\n    sitemap: 'https://ashishkamat.dev/sitemap.xml',\n  }\n}\n"], "names": [], "mappings": ";;;AAEe,SAAS;IACtB,OAAO;QACL,OAAO;YACL,WAAW;YACX,OAAO;YACP,UAAU;gBAAC;gBAAa;aAAU;QACpC;QACA,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/app/robots--route-entry.js"], "sourcesContent": ["            import { NextResponse } from 'next/server'\n            import handler from \"./robots.ts\"\n            import { resolveRouteData } from\n'next/dist/build/webpack/loaders/metadata/resolve-route-data'\n\n            const contentType = \"text/plain\"\n            const cacheControl = \"public, max-age=0, must-revalidate\"\n            const fileType = \"robots\"\n\n            if (typeof handler !== 'function') {\n                throw new Error('Default export is missing in \"./robots.ts\"')\n            }\n\n            export async function GET() {\n              const data = await handler()\n              const content = resolveRouteData(data, fileType)\n\n              return new NextResponse(content, {\n                headers: {\n                  'Content-Type': contentType,\n                  'Cache-Control': cacheControl,\n                },\n              })\n            }\n        "], "names": [], "mappings": ";;;AAAY;AACA;AACA;;;;AAGA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,WAAW;AAEjB,IAAI,OAAO,oHAAA,CAAA,UAAO,KAAK,YAAY;IAC/B,MAAM,IAAI,MAAM;AACpB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACzB,MAAM,UAAU,CAAA,GAAA,mMAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;IAEvC,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,SAAS;QAC/B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;IACF;AACF", "debugId": null}}]}