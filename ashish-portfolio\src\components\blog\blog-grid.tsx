"use client";

import { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Calendar, Clock, ArrowRight, Tag, Eye } from "lucide-react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

const blogPosts = [
  {
    id: 1,
    title: "Building Scalable React Applications with TypeScript",
    excerpt: "Learn how to structure large React applications using TypeScript, advanced patterns, and best practices for maintainability and performance.",
    content: "In this comprehensive guide, we'll explore how to build scalable React applications using TypeScript...",
    slug: "building-scalable-react-applications-typescript",
    category: "React",
    tags: ["React", "TypeScript", "Architecture", "Best Practices"],
    publishedAt: "2024-01-15",
    readTime: "8 min read",
    views: 1250,
    featured: true,
    author: "Ashish Kamat"
  },
  {
    id: 2,
    title: "Next.js 14 App Router: Complete Guide",
    excerpt: "Dive deep into Next.js 14's App Router, exploring new features, migration strategies, and performance optimizations.",
    content: "Next.js 14 introduces significant improvements to the App Router...",
    slug: "nextjs-14-app-router-complete-guide",
    category: "Next.js",
    tags: ["Next.js", "React", "App Router", "Performance"],
    publishedAt: "2024-01-10",
    readTime: "12 min read",
    views: 980,
    featured: true,
    author: "Ashish Kamat"
  },
  {
    id: 3,
    title: "Mastering CSS Grid and Flexbox in 2024",
    excerpt: "A comprehensive guide to modern CSS layout techniques, comparing Grid and Flexbox with practical examples and use cases.",
    content: "CSS Grid and Flexbox are powerful layout systems that have revolutionized web design...",
    slug: "mastering-css-grid-flexbox-2024",
    category: "CSS",
    tags: ["CSS", "Grid", "Flexbox", "Layout"],
    publishedAt: "2024-01-05",
    readTime: "10 min read",
    views: 750,
    featured: false,
    author: "Ashish Kamat"
  },
  {
    id: 4,
    title: "State Management in React: Redux vs Zustand vs Context",
    excerpt: "Compare different state management solutions for React applications and learn when to use each approach.",
    content: "State management is a crucial aspect of React development...",
    slug: "react-state-management-redux-zustand-context",
    category: "React",
    tags: ["React", "State Management", "Redux", "Zustand"],
    publishedAt: "2023-12-28",
    readTime: "15 min read",
    views: 1100,
    featured: false,
    author: "Ashish Kamat"
  },
  {
    id: 5,
    title: "Building RESTful APIs with Node.js and Express",
    excerpt: "Learn how to create robust and scalable REST APIs using Node.js, Express, and modern JavaScript patterns.",
    content: "Building APIs is a fundamental skill for full-stack developers...",
    slug: "building-restful-apis-nodejs-express",
    category: "Backend",
    tags: ["Node.js", "Express", "API", "Backend"],
    publishedAt: "2023-12-20",
    readTime: "11 min read",
    views: 890,
    featured: false,
    author: "Ashish Kamat"
  },
  {
    id: 6,
    title: "Database Design Best Practices for Web Applications",
    excerpt: "Essential database design principles, normalization techniques, and optimization strategies for modern web applications.",
    content: "Good database design is the foundation of any successful web application...",
    slug: "database-design-best-practices-web-applications",
    category: "Database",
    tags: ["Database", "PostgreSQL", "Design", "Optimization"],
    publishedAt: "2023-12-15",
    readTime: "9 min read",
    views: 650,
    featured: false,
    author: "Ashish Kamat"
  }
];

const categories = ["All", "React", "Next.js", "CSS", "Backend", "Database"];

export function BlogGrid() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const filteredPosts = selectedCategory === "All" 
    ? blogPosts 
    : blogPosts.filter(post => post.category === selectedCategory);

  const featuredPosts = blogPosts.filter(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <section ref={ref} className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Featured Posts */}
        {selectedCategory === "All" && featuredPosts.length > 0 && (
          <div className="mb-16">
            <motion.h2
              className="text-2xl font-bold mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6 }}
            >
              Featured Articles
            </motion.h2>
            
            <div className="grid lg:grid-cols-2 gap-8">
              {featuredPosts.map((post, index) => (
                <motion.div
                  key={post.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={inView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="h-full hover-lift group border-border/50 hover:border-border transition-all duration-300">
                    <div className="relative h-48 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-t-lg">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-6xl opacity-20">📝</div>
                      </div>
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-yellow-500 text-yellow-900">Featured</Badge>
                      </div>
                      <div className="absolute top-4 right-4">
                        <Badge variant="secondary">{post.category}</Badge>
                      </div>
                    </div>
                    
                    <CardHeader>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(post.publishedAt)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{post.readTime}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Eye className="h-4 w-4" />
                          <span>{post.views}</span>
                        </div>
                      </div>
                      <h3 className="text-xl font-bold group-hover:text-primary transition-colors duration-300 line-clamp-2">
                        {post.title}
                      </h3>
                    </CardHeader>
                    
                    <CardContent>
                      <p className="text-muted-foreground mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>
                      
                      <div className="flex flex-wrap gap-2 mb-4">
                        {post.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      
                      <Button asChild variant="ghost" className="group p-0 h-auto">
                        <Link href={`/blog/${post.slug}`}>
                          Read More
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-2 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="transition-all duration-300"
            >
              {category}
            </Button>
          ))}
        </motion.div>

        {/* All Posts */}
        <div>
          <motion.h2
            className="text-2xl font-bold mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {selectedCategory === "All" ? "All Articles" : `${selectedCategory} Articles`}
          </motion.h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {(selectedCategory === "All" ? regularPosts : filteredPosts).map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={inView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                layout
              >
                <Card className="h-full hover-lift group border-border/50 hover:border-border transition-all duration-300">
                  <div className="relative h-40 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-t-lg">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-4xl opacity-20">📄</div>
                    </div>
                    <div className="absolute top-3 right-3">
                      <Badge variant="secondary" className="text-xs">{post.category}</Badge>
                    </div>
                  </div>
                  
                  <CardHeader className="pb-2">
                    <div className="flex items-center space-x-3 text-xs text-muted-foreground mb-2">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(post.publishedAt)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{post.readTime}</span>
                      </div>
                    </div>
                    <h3 className="font-bold group-hover:text-primary transition-colors duration-300 line-clamp-2">
                      {post.title}
                    </h3>
                  </CardHeader>
                  
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex flex-wrap gap-1 mb-3">
                      {post.tags.slice(0, 2).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <Button asChild variant="ghost" size="sm" className="group p-0 h-auto">
                      <Link href={`/blog/${post.slug}`}>
                        Read More
                        <ArrowRight className="ml-1 h-3 w-3 group-hover:translate-x-1 transition-transform duration-300" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
