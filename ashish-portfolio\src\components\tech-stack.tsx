"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

const techStacks = {
  frontend: [
    { name: "React", icon: "⚛️", color: "text-blue-500" },
    { name: "Next.js", icon: "▲", color: "text-black dark:text-white" },
    { name: "TypeScript", icon: "📘", color: "text-blue-600" },
    { name: "Tailwind CSS", icon: "🎨", color: "text-cyan-500" },
    { name: "Framer Motion", icon: "🎭", color: "text-purple-500" },
    { name: "Zustand", icon: "🐻", color: "text-orange-500" },
    { name: "React Hook Form", icon: "📝", color: "text-pink-500" },
    { name: "Zod", icon: "🛡️", color: "text-blue-700" },
  ],
  backend: [
    { name: "Node.js", icon: "🟢", color: "text-green-600" },
    { name: "Express.js", icon: "🚀", color: "text-gray-600" },
    { name: "PostgreSQL", icon: "🐘", color: "text-blue-800" },
    { name: "MongoDB", icon: "🍃", color: "text-green-500" },
    { name: "Prisma", icon: "🔷", color: "text-indigo-600" },
    { name: "Supabase", icon: "⚡", color: "text-green-400" },
    { name: "Redis", icon: "🔴", color: "text-red-500" },
    { name: "GraphQL", icon: "💜", color: "text-pink-600" },
  ],
  tools: [
    { name: "Git", icon: "📚", color: "text-orange-600" },
    { name: "GitHub", icon: "🐙", color: "text-gray-800 dark:text-gray-200" },
    { name: "Vercel", icon: "▲", color: "text-black dark:text-white" },
    { name: "AWS", icon: "☁️", color: "text-orange-500" },
    { name: "Docker", icon: "🐳", color: "text-blue-500" },
    { name: "VS Code", icon: "💙", color: "text-blue-600" },
    { name: "Figma", icon: "🎨", color: "text-purple-500" },
    { name: "Linear", icon: "📊", color: "text-indigo-500" },
  ],
};

const TechRow = ({ 
  techs, 
  direction = "left", 
  speed = 30 
}: { 
  techs: typeof techStacks.frontend; 
  direction?: "left" | "right"; 
  speed?: number; 
}) => {
  return (
    <div className="flex overflow-hidden">
      <motion.div
        className="flex space-x-8 whitespace-nowrap"
        animate={{
          x: direction === "left" ? [0, -1000] : [-1000, 0],
        }}
        transition={{
          x: {
            repeat: Infinity,
            repeatType: "loop",
            duration: speed,
            ease: "linear",
          },
        }}
      >
        {/* Duplicate the array to create seamless loop */}
        {[...techs, ...techs].map((tech, index) => (
          <motion.div
            key={`${tech.name}-${index}`}
            className="flex items-center space-x-3 bg-card border border-border rounded-lg px-6 py-3 shadow-sm hover:shadow-md transition-shadow duration-200"
            whileHover={{ scale: 1.05 }}
          >
            <span className="text-2xl">{tech.icon}</span>
            <span className={`font-medium ${tech.color}`}>{tech.name}</span>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export function TechStack() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section ref={ref} className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Technologies I Love</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            I'm passionate about cutting-edge technologies and constantly learning new tools 
            to build amazing digital experiences.
          </p>
        </motion.div>

        <motion.div
          className="space-y-8"
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {/* Frontend Technologies */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-center text-muted-foreground">
              Frontend & UI
            </h3>
            <TechRow techs={techStacks.frontend} direction="left" speed={25} />
          </div>

          {/* Backend Technologies */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-center text-muted-foreground">
              Backend & Database
            </h3>
            <TechRow techs={techStacks.backend} direction="right" speed={30} />
          </div>

          {/* Tools & Platforms */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-center text-muted-foreground">
              Tools & Platforms
            </h3>
            <TechRow techs={techStacks.tools} direction="left" speed={35} />
          </div>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {[
            { number: "3+", label: "Years Experience" },
            { number: "50+", label: "Projects Completed" },
            { number: "20+", label: "Happy Clients" },
            { number: "99%", label: "Client Satisfaction" },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={inView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
            >
              <div className="text-3xl sm:text-4xl font-bold gradient-text-blue mb-2">
                {stat.number}
              </div>
              <div className="text-sm text-muted-foreground">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
