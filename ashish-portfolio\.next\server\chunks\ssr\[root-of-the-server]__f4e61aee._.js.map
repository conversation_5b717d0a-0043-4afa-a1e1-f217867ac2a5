{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/command-palette.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { \n  Search, \n  Home, \n  User, \n  Briefcase, \n  Mail, \n  FileText,\n  ExternalLink,\n  Github,\n  Linkedin,\n  Twitter\n} from \"lucide-react\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface Command {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<any>;\n  action: () => void;\n  category: string;\n  keywords: string[];\n}\n\ninterface CommandPaletteProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {\n  const [search, setSearch] = useState(\"\");\n  const [selectedIndex, setSelectedIndex] = useState(0);\n  const router = useRouter();\n\n  const commands: Command[] = [\n    // Navigation\n    {\n      id: \"home\",\n      title: \"Home\",\n      description: \"Go to homepage\",\n      icon: Home,\n      action: () => router.push(\"/\"),\n      category: \"Navigation\",\n      keywords: [\"home\", \"main\", \"landing\"],\n    },\n    {\n      id: \"about\",\n      title: \"About\",\n      description: \"Learn more about me\",\n      icon: User,\n      action: () => router.push(\"/about\"),\n      category: \"Navigation\",\n      keywords: [\"about\", \"bio\", \"experience\", \"skills\"],\n    },\n    {\n      id: \"projects\",\n      title: \"Projects\",\n      description: \"View my work and projects\",\n      icon: Briefcase,\n      action: () => router.push(\"/projects\"),\n      category: \"Navigation\",\n      keywords: [\"projects\", \"work\", \"portfolio\", \"showcase\"],\n    },\n    {\n      id: \"blog\",\n      title: \"Blog\",\n      description: \"Read my latest articles\",\n      icon: FileText,\n      action: () => router.push(\"/blog\"),\n      category: \"Navigation\",\n      keywords: [\"blog\", \"articles\", \"writing\", \"posts\"],\n    },\n    {\n      id: \"contact\",\n      title: \"Contact\",\n      description: \"Get in touch with me\",\n      icon: Mail,\n      action: () => router.push(\"/contact\"),\n      category: \"Navigation\",\n      keywords: [\"contact\", \"email\", \"message\", \"hire\"],\n    },\n    // External Links\n    {\n      id: \"github\",\n      title: \"GitHub\",\n      description: \"View my GitHub profile\",\n      icon: Github,\n      action: () => window.open(\"https://github.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"github\", \"code\", \"repositories\", \"open source\"],\n    },\n    {\n      id: \"linkedin\",\n      title: \"LinkedIn\",\n      description: \"Connect with me on LinkedIn\",\n      icon: Linkedin,\n      action: () => window.open(\"https://linkedin.com/in/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"linkedin\", \"professional\", \"network\", \"career\"],\n    },\n    {\n      id: \"twitter\",\n      title: \"Twitter\",\n      description: \"Follow me on Twitter\",\n      icon: Twitter,\n      action: () => window.open(\"https://twitter.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"twitter\", \"social\", \"updates\", \"thoughts\"],\n    },\n    // Quick Actions\n    {\n      id: \"email\",\n      title: \"Send Email\",\n      description: \"Send me an email directly\",\n      icon: Mail,\n      action: () => window.open(\"mailto:<EMAIL>\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"email\", \"contact\", \"message\", \"hire\"],\n    },\n    {\n      id: \"resume\",\n      title: \"Download Resume\",\n      description: \"Download my latest resume\",\n      icon: ExternalLink,\n      action: () => window.open(\"/resume.pdf\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"resume\", \"cv\", \"download\", \"hire\"],\n    },\n  ];\n\n  const filteredCommands = commands.filter((command) => {\n    const searchLower = search.toLowerCase();\n    return (\n      command.title.toLowerCase().includes(searchLower) ||\n      command.description.toLowerCase().includes(searchLower) ||\n      command.keywords.some((keyword) => keyword.includes(searchLower))\n    );\n  });\n\n  const groupedCommands = filteredCommands.reduce((acc, command) => {\n    if (!acc[command.category]) {\n      acc[command.category] = [];\n    }\n    acc[command.category].push(command);\n    return acc;\n  }, {} as Record<string, Command[]>);\n\n  useEffect(() => {\n    setSelectedIndex(0);\n  }, [search]);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open) return;\n\n      if (e.key === \"ArrowDown\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev < filteredCommands.length - 1 ? prev + 1 : 0\n        );\n      } else if (e.key === \"ArrowUp\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev > 0 ? prev - 1 : filteredCommands.length - 1\n        );\n      } else if (e.key === \"Enter\") {\n        e.preventDefault();\n        if (filteredCommands[selectedIndex]) {\n          filteredCommands[selectedIndex].action();\n          onOpenChange(false);\n          setSearch(\"\");\n        }\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [open, selectedIndex, filteredCommands, onOpenChange]);\n\n  const handleCommandSelect = (command: Command) => {\n    command.action();\n    onOpenChange(false);\n    setSearch(\"\");\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl p-0 overflow-hidden\">\n        <DialogHeader className=\"p-4 pb-0\">\n          <DialogTitle className=\"sr-only\">Command Palette</DialogTitle>\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Type a command or search...\"\n              value={search}\n              onChange={(e) => setSearch(e.target.value)}\n              className=\"pl-10 border-0 focus-visible:ring-0 text-base\"\n              autoFocus\n            />\n          </div>\n        </DialogHeader>\n\n        <div className=\"max-h-96 overflow-y-auto p-4 pt-0\">\n          {Object.keys(groupedCommands).length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No commands found for \"{search}\"\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {Object.entries(groupedCommands).map(([category, commands]) => (\n                <div key={category}>\n                  <div className=\"text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2\">\n                    {category}\n                  </div>\n                  <div className=\"space-y-1\">\n                    {commands.map((command, index) => {\n                      const globalIndex = filteredCommands.indexOf(command);\n                      const Icon = command.icon;\n                      \n                      return (\n                        <motion.button\n                          key={command.id}\n                          onClick={() => handleCommandSelect(command)}\n                          className={`w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ${\n                            globalIndex === selectedIndex\n                              ? \"bg-accent text-accent-foreground\"\n                              : \"hover:bg-accent/50\"\n                          }`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <div className={`p-2 rounded-md ${\n                            globalIndex === selectedIndex\n                              ? \"bg-primary text-primary-foreground\"\n                              : \"bg-muted\"\n                          }`}>\n                            <Icon className=\"h-4 w-4\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"font-medium\">{command.title}</div>\n                            <div className=\"text-sm text-muted-foreground truncate\">\n                              {command.description}\n                            </div>\n                          </div>\n                          {command.category === \"Social\" && (\n                            <ExternalLink className=\"h-3 w-3 text-muted-foreground\" />\n                          )}\n                        </motion.button>\n                      );\n                    })}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-t p-3 text-xs text-muted-foreground flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↑↓</Badge>\n              <span>Navigate</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↵</Badge>\n              <span>Select</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">Esc</Badge>\n              <span>Close</span>\n            </div>\n          </div>\n          <div className=\"text-muted-foreground/60\">\n            {filteredCommands.length} result{filteredCommands.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAMA;AACA;AAxBA;;;;;;;;;AAyCO,SAAS,eAAe,EAAE,IAAI,EAAE,YAAY,EAAuB;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,WAAsB;QAC1B,aAAa;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,mMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAQ;aAAU;QACvC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAO;gBAAc;aAAS;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,4MAAA,CAAA,YAAS;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAQ;gBAAa;aAAW;QACzD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,8MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAY;gBAAW;aAAQ;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAS;gBAAW;aAAO;QACnD;QACA,iBAAiB;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,SAAM;YACZ,QAAQ,IAAM,OAAO,IAAI,CAAC,kCAAkC;YAC5D,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAQ;gBAAgB;aAAc;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,0MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC,uCAAuC;YACjE,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAgB;gBAAW;aAAS;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,wMAAA,CAAA,UAAO;YACb,QAAQ,IAAM,OAAO,IAAI,CAAC,mCAAmC;YAC7D,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAU;gBAAW;aAAW;QACxD;QACA,gBAAgB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC,gCAAgC;YAC1D,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAW;gBAAW;aAAO;QACnD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sNAAA,CAAA,eAAY;YAClB,QAAQ,IAAM,OAAO,IAAI,CAAC,eAAe;YACzC,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAM;gBAAY;aAAO;QAChD;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,cAAc,OAAO,WAAW;QACtC,OACE,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC3C,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,QAAQ,CAAC;IAExD;IAEA,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;QAC5B;QACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC3B,OAAO;IACT,GAAG,CAAC;IAEJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;IACnB,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,MAAM;YAEX,IAAI,EAAE,GAAG,KAAK,aAAa;gBACzB,EAAE,cAAc;gBAChB,iBAAiB,CAAC,OAChB,OAAO,iBAAiB,MAAM,GAAG,IAAI,OAAO,IAAI;YAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;gBAC9B,EAAE,cAAc;gBAChB,iBAAiB,CAAC,OAChB,OAAO,IAAI,OAAO,IAAI,iBAAiB,MAAM,GAAG;YAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;gBAC5B,EAAE,cAAc;gBAChB,IAAI,gBAAgB,CAAC,cAAc,EAAE;oBACnC,gBAAgB,CAAC,cAAc,CAAC,MAAM;oBACtC,aAAa;oBACb,UAAU;gBACZ;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;QAAM;QAAe;QAAkB;KAAa;IAExD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,MAAM;QACd,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAU;;;;;;sCACjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,SAAS;;;;;;;;;;;;;;;;;;8BAKf,8OAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,kBACvC,8OAAC;wBAAI,WAAU;;4BAAyC;4BAC9B;4BAAO;;;;;;6CAGjC,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,UAAU,SAAS,iBACxD,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4CACtB,MAAM,cAAc,iBAAiB,OAAO,CAAC;4CAC7C,MAAM,OAAO,QAAQ,IAAI;4CAEzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAC,2FAA2F,EACrG,gBAAgB,gBACZ,qCACA,sBACJ;gDACF,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,8OAAC;wDAAI,WAAW,CAAC,eAAe,EAC9B,gBAAgB,gBACZ,uCACA,YACJ;kEACA,cAAA,8OAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAe,QAAQ,KAAK;;;;;;0EAC3C,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,WAAW;;;;;;;;;;;;oDAGvB,QAAQ,QAAQ,KAAK,0BACpB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;+CAxBrB,QAAQ,EAAE;;;;;wCA4BrB;;;;;;;+BAvCM;;;;;;;;;;;;;;;8BA+ClB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAGV,8OAAC;4BAAI,WAAU;;gCACZ,iBAAiB,MAAM;gCAAC;gCAAQ,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMnF", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { useTheme } from \"next-themes\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Moon,\n  Sun,\n  Menu,\n  X,\n  Home,\n  User,\n  Briefcase,\n  Mail,\n  FileText,\n  Search\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { CommandPalette } from \"@/components/command-palette\";\nimport { cn } from \"@/lib/utils\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\", icon: Home },\n  { name: \"About\", href: \"/about\", icon: User },\n  { name: \"Projects\", href: \"/projects\", icon: Briefcase },\n  { name: \"Blog\", href: \"/blog\", icon: FileText },\n  { name: \"Contact\", href: \"/contact\", icon: Mail },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.metaKey || e.ctrlKey) && e.key === \"k\") {\n        e.preventDefault();\n        setCommandPaletteOpen(true);\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, []);\n\n  const toggleTheme = () => {\n    setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n  };\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <motion.header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        scrolled\n          ? \"bg-background/80 backdrop-blur-md border-b border-border\"\n          : \"bg-transparent\"\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <nav className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex-shrink-0\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue\">\n              AK\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-foreground/80 hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-accent\"\n                  >\n                    {item.name}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Command Palette, Theme Toggle & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setCommandPaletteOpen(true)}\n              className=\"hidden md:flex items-center space-x-2 text-muted-foreground hover:text-foreground\"\n            >\n              <Search className=\"h-4 w-4\" />\n              <span className=\"text-sm\">Search</span>\n              <kbd className=\"pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100\">\n                <span className=\"text-xs\">⌘</span>K\n              </kbd>\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={toggleTheme}\n              className=\"w-9 h-9\"\n            >\n              {theme === \"dark\" ? (\n                <Sun className=\"h-4 w-4\" />\n              ) : (\n                <Moon className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"w-9 h-9\"\n              >\n                {isOpen ? (\n                  <X className=\"h-4 w-4\" />\n                ) : (\n                  <Menu className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background/95 backdrop-blur-md rounded-lg mt-2 border border-border\">\n                {navItems.map((item) => {\n                  const Icon = item.icon;\n                  return (\n                    <motion.div\n                      key={item.name}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Link\n                        href={item.href}\n                        className=\"text-foreground/80 hover:text-foreground flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-accent\"\n                        onClick={() => setIsOpen(false)}\n                      >\n                        <Icon className=\"h-4 w-4 mr-3\" />\n                        {item.name}\n                      </Link>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n\n      <CommandPalette\n        open={commandPaletteOpen}\n        onOpenChange={setCommandPaletteOpen}\n      />\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AApBA;;;;;;;;;;AAsBA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,kMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4MAAA,CAAA,YAAS;IAAC;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,kMAAA,CAAA,OAAI;IAAC;CACjD;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,sBAAsB;YACxB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,WACI,6DACA;QAEN,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwC;;;;;;;;;;;0CAMnE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CARP,KAAK,IAAI;;;;;;;;;;;;;;;0CAgBtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;;;;;;kDAItC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,UAAU,uBACT,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAKpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU,CAAC;4CAC1B,WAAU;sDAET,uBACC,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAEb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1B,8OAAC,yLAAA,CAAA,kBAAe;kCACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,UAAU;;8DAEzB,8OAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAVP,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOV,8OAAC,wIAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/hero-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { ArrowDown, Download, Mail, Github, Linkedin, Twitter } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst roles = [\n  \"Full Stack Developer\",\n  \"UI/UX Designer\", \n  \"React Specialist\",\n  \"TypeScript Expert\",\n  \"Problem Solver\"\n];\n\nconst socialLinks = [\n  { icon: Github, href: \"https://github.com/ashishkamat\", label: \"GitHub\" },\n  { icon: Linkedin, href: \"https://linkedin.com/in/ashishkamat\", label: \"LinkedIn\" },\n  { icon: Twitter, href: \"https://twitter.com/ashishkamat\", label: \"Twitter\" },\n];\n\nexport function HeroSection() {\n  const [currentRole, setCurrentRole] = useState(0);\n  const [displayText, setDisplayText] = useState(\"\");\n  const [isTyping, setIsTyping] = useState(true);\n\n  useEffect(() => {\n    const role = roles[currentRole];\n    let index = 0;\n    \n    const typeText = () => {\n      if (index < role.length) {\n        setDisplayText(role.slice(0, index + 1));\n        index++;\n        setTimeout(typeText, 100);\n      } else {\n        setTimeout(() => {\n          setIsTyping(false);\n          setTimeout(() => {\n            setDisplayText(\"\");\n            setCurrentRole((prev) => (prev + 1) % roles.length);\n            setIsTyping(true);\n          }, 1000);\n        }, 2000);\n      }\n    };\n\n    if (isTyping) {\n      typeText();\n    }\n  }, [currentRole, isTyping]);\n\n  return (\n    <section className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-float\" />\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-float\" style={{ animationDelay: \"1s\" }} />\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-green-500/10 rounded-full blur-3xl animate-float\" style={{ animationDelay: \"2s\" }} />\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 pt-16\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <div className=\"space-y-4\">\n              <motion.p\n                className=\"text-lg text-muted-foreground\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2 }}\n              >\n                Hello, I'm\n              </motion.p>\n              \n              <motion.h1\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                <span className=\"gradient-text-blue\">Ashish Kamat</span>\n              </motion.h1>\n\n              <div className=\"h-16 flex items-center\">\n                <motion.h2\n                  className=\"text-2xl sm:text-3xl lg:text-4xl font-semibold text-foreground/80\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.4 }}\n                >\n                  a{\" \"}\n                  <span className=\"gradient-text relative\">\n                    {displayText}\n                    <span className=\"animate-pulse\">|</span>\n                  </span>\n                </motion.h2>\n              </div>\n\n              <motion.p\n                className=\"text-lg text-muted-foreground max-w-2xl leading-relaxed\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.5 }}\n              >\n                I help founders and businesses turn ideas into seamless digital experiences. \n                From concept to deployment, I create modern web applications that make a difference.\n              </motion.p>\n            </div>\n\n            {/* CTA Buttons */}\n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 }}\n            >\n              <Button asChild size=\"lg\" className=\"group\">\n                <Link href=\"/contact\">\n                  <Mail className=\"mr-2 h-4 w-4 group-hover:animate-bounce\" />\n                  Get In Touch\n                </Link>\n              </Button>\n              \n              <Button variant=\"outline\" size=\"lg\" className=\"group\">\n                <Download className=\"mr-2 h-4 w-4 group-hover:animate-bounce\" />\n                Download CV\n              </Button>\n            </motion.div>\n\n            {/* Social Links */}\n            <motion.div\n              className=\"flex space-x-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.7 }}\n            >\n              {socialLinks.map((social, index) => {\n                const Icon = social.icon;\n                return (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"p-3 rounded-full bg-accent hover:bg-accent/80 transition-colors duration-200 group\"\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.8 + index * 0.1 }}\n                  >\n                    <Icon className=\"h-5 w-5 group-hover:text-primary transition-colors duration-200\" />\n                  </motion.a>\n                );\n              })}\n            </motion.div>\n          </motion.div>\n\n          {/* Profile Image */}\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <div className=\"relative w-80 h-80 mx-auto lg:w-96 lg:h-96\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse-glow\" />\n              <div className=\"absolute inset-2 bg-background rounded-full overflow-hidden\">\n                <Image\n                  src=\"/ashish-profile.svg\"\n                  alt=\"Ashish Kamat\"\n                  fill\n                  className=\"object-cover\"\n                  priority\n                />\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1 }}\n        >\n          <motion.div\n            className=\"flex flex-col items-center space-y-2 text-muted-foreground\"\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n          >\n            <span className=\"text-sm\">Scroll to explore</span>\n            <ArrowDown className=\"h-4 w-4\" />\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;;AASA,MAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,cAAc;IAClB;QAAE,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAkC,OAAO;IAAS;IACxE;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAuC,OAAO;IAAW;IACjF;QAAE,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAmC,OAAO;IAAU;CAC5E;AAEM,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,KAAK,CAAC,YAAY;QAC/B,IAAI,QAAQ;QAEZ,MAAM,WAAW;YACf,IAAI,QAAQ,KAAK,MAAM,EAAE;gBACvB,eAAe,KAAK,KAAK,CAAC,GAAG,QAAQ;gBACrC;gBACA,WAAW,UAAU;YACvB,OAAO;gBACL,WAAW;oBACT,YAAY;oBACZ,WAAW;wBACT,eAAe;wBACf,eAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;wBAClD,YAAY;oBACd,GAAG;gBACL,GAAG;YACL;QACF;QAEA,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;QAAa;KAAS;IAE1B,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA+F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCAC5I,8OAAC;wBAAI,WAAU;wBAAsI,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAGrL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;0DAC1B;;;;;;0DAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;0DAEzB,cAAA,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;0DAGvC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oDACR,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO;oDAAI;;wDAC1B;wDACG;sEACF,8OAAC;4DAAK,WAAU;;gEACb;8EACD,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;0DAKtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;0DAC1B;;;;;;;;;;;;kDAOH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,MAAK;gDAAK,WAAU;0DAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAA4C;;;;;;;;;;;;0DAKhE,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;kEAC5C,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAA4C;;;;;;;;;;;;;kDAMpE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAExB,YAAY,GAAG,CAAC,CAAC,QAAQ;4CACxB,MAAM,OAAO,OAAO,IAAI;4CACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,OAAO,IAAI;gDACjB,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAI;gDACvB,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,MAAM,QAAQ;gDAAI;0DAEvC,cAAA,8OAAC;oDAAK,WAAU;;;;;;+CAXX,OAAO,KAAK;;;;;wCAcvB;;;;;;;;;;;;0CAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;gDACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAE;kCAEvB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;;8CAE5C,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjC", "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/tech-stack.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\n\nconst techStacks = {\n  frontend: [\n    { name: \"React\", icon: \"⚛️\", color: \"text-blue-500\" },\n    { name: \"Next.js\", icon: \"▲\", color: \"text-black dark:text-white\" },\n    { name: \"TypeScript\", icon: \"📘\", color: \"text-blue-600\" },\n    { name: \"Tailwind CSS\", icon: \"🎨\", color: \"text-cyan-500\" },\n    { name: \"Framer Motion\", icon: \"🎭\", color: \"text-purple-500\" },\n    { name: \"Zustand\", icon: \"🐻\", color: \"text-orange-500\" },\n    { name: \"React Hook Form\", icon: \"📝\", color: \"text-pink-500\" },\n    { name: \"Zod\", icon: \"🛡️\", color: \"text-blue-700\" },\n  ],\n  backend: [\n    { name: \"Node.js\", icon: \"🟢\", color: \"text-green-600\" },\n    { name: \"Express.js\", icon: \"🚀\", color: \"text-gray-600\" },\n    { name: \"PostgreSQL\", icon: \"🐘\", color: \"text-blue-800\" },\n    { name: \"MongoDB\", icon: \"🍃\", color: \"text-green-500\" },\n    { name: \"Prisma\", icon: \"🔷\", color: \"text-indigo-600\" },\n    { name: \"Supabase\", icon: \"⚡\", color: \"text-green-400\" },\n    { name: \"Redis\", icon: \"🔴\", color: \"text-red-500\" },\n    { name: \"GraphQL\", icon: \"💜\", color: \"text-pink-600\" },\n  ],\n  tools: [\n    { name: \"Git\", icon: \"📚\", color: \"text-orange-600\" },\n    { name: \"GitHub\", icon: \"🐙\", color: \"text-gray-800 dark:text-gray-200\" },\n    { name: \"Vercel\", icon: \"▲\", color: \"text-black dark:text-white\" },\n    { name: \"AWS\", icon: \"☁️\", color: \"text-orange-500\" },\n    { name: \"Docker\", icon: \"🐳\", color: \"text-blue-500\" },\n    { name: \"VS Code\", icon: \"💙\", color: \"text-blue-600\" },\n    { name: \"Figma\", icon: \"🎨\", color: \"text-purple-500\" },\n    { name: \"Linear\", icon: \"📊\", color: \"text-indigo-500\" },\n  ],\n};\n\nconst TechRow = ({ \n  techs, \n  direction = \"left\", \n  speed = 30 \n}: { \n  techs: typeof techStacks.frontend; \n  direction?: \"left\" | \"right\"; \n  speed?: number; \n}) => {\n  return (\n    <div className=\"flex overflow-hidden\">\n      <motion.div\n        className=\"flex space-x-8 whitespace-nowrap\"\n        animate={{\n          x: direction === \"left\" ? [0, -1000] : [-1000, 0],\n        }}\n        transition={{\n          x: {\n            repeat: Infinity,\n            repeatType: \"loop\",\n            duration: speed,\n            ease: \"linear\",\n          },\n        }}\n      >\n        {/* Duplicate the array to create seamless loop */}\n        {[...techs, ...techs].map((tech, index) => (\n          <motion.div\n            key={`${tech.name}-${index}`}\n            className=\"flex items-center space-x-3 bg-card border border-border rounded-lg px-6 py-3 shadow-sm hover:shadow-md transition-shadow duration-200\"\n            whileHover={{ scale: 1.05 }}\n          >\n            <span className=\"text-2xl\">{tech.icon}</span>\n            <span className={`font-medium ${tech.color}`}>{tech.name}</span>\n          </motion.div>\n        ))}\n      </motion.div>\n    </div>\n  );\n};\n\nexport function TechStack() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Technologies I Love</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            I'm passionate about cutting-edge technologies and constantly learning new tools \n            to build amazing digital experiences.\n          </p>\n        </motion.div>\n\n        <motion.div\n          className=\"space-y-8\"\n          initial={{ opacity: 0 }}\n          animate={inView ? { opacity: 1 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          {/* Frontend Technologies */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-xl font-semibold text-center text-muted-foreground\">\n              Frontend & UI\n            </h3>\n            <TechRow techs={techStacks.frontend} direction=\"left\" speed={25} />\n          </div>\n\n          {/* Backend Technologies */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-xl font-semibold text-center text-muted-foreground\">\n              Backend & Database\n            </h3>\n            <TechRow techs={techStacks.backend} direction=\"right\" speed={30} />\n          </div>\n\n          {/* Tools & Platforms */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-xl font-semibold text-center text-muted-foreground\">\n              Tools & Platforms\n            </h3>\n            <TechRow techs={techStacks.tools} direction=\"left\" speed={35} />\n          </div>\n        </motion.div>\n\n        {/* Stats Section */}\n        <motion.div\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mt-20\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.4 }}\n        >\n          {[\n            { number: \"3+\", label: \"Years Experience\" },\n            { number: \"50+\", label: \"Projects Completed\" },\n            { number: \"20+\", label: \"Happy Clients\" },\n            { number: \"99%\", label: \"Client Satisfaction\" },\n          ].map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              className=\"text-center\"\n              initial={{ opacity: 0, scale: 0.5 }}\n              animate={inView ? { opacity: 1, scale: 1 } : {}}\n              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}\n            >\n              <div className=\"text-3xl sm:text-4xl font-bold gradient-text-blue mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">{stat.label}</div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,UAAU;QACR;YAAE,MAAM;YAAS,MAAM;YAAM,OAAO;QAAgB;QACpD;YAAE,MAAM;YAAW,MAAM;YAAK,OAAO;QAA6B;QAClE;YAAE,MAAM;YAAc,MAAM;YAAM,OAAO;QAAgB;QACzD;YAAE,MAAM;YAAgB,MAAM;YAAM,OAAO;QAAgB;QAC3D;YAAE,MAAM;YAAiB,MAAM;YAAM,OAAO;QAAkB;QAC9D;YAAE,MAAM;YAAW,MAAM;YAAM,OAAO;QAAkB;QACxD;YAAE,MAAM;YAAmB,MAAM;YAAM,OAAO;QAAgB;QAC9D;YAAE,MAAM;YAAO,MAAM;YAAO,OAAO;QAAgB;KACpD;IACD,SAAS;QACP;YAAE,MAAM;YAAW,MAAM;YAAM,OAAO;QAAiB;QACvD;YAAE,MAAM;YAAc,MAAM;YAAM,OAAO;QAAgB;QACzD;YAAE,MAAM;YAAc,MAAM;YAAM,OAAO;QAAgB;QACzD;YAAE,MAAM;YAAW,MAAM;YAAM,OAAO;QAAiB;QACvD;YAAE,MAAM;YAAU,MAAM;YAAM,OAAO;QAAkB;QACvD;YAAE,MAAM;YAAY,MAAM;YAAK,OAAO;QAAiB;QACvD;YAAE,MAAM;YAAS,MAAM;YAAM,OAAO;QAAe;QACnD;YAAE,MAAM;YAAW,MAAM;YAAM,OAAO;QAAgB;KACvD;IACD,OAAO;QACL;YAAE,MAAM;YAAO,MAAM;YAAM,OAAO;QAAkB;QACpD;YAAE,MAAM;YAAU,MAAM;YAAM,OAAO;QAAmC;QACxE;YAAE,MAAM;YAAU,MAAM;YAAK,OAAO;QAA6B;QACjE;YAAE,MAAM;YAAO,MAAM;YAAM,OAAO;QAAkB;QACpD;YAAE,MAAM;YAAU,MAAM;YAAM,OAAO;QAAgB;QACrD;YAAE,MAAM;YAAW,MAAM;YAAM,OAAO;QAAgB;QACtD;YAAE,MAAM;YAAS,MAAM;YAAM,OAAO;QAAkB;QACtD;YAAE,MAAM;YAAU,MAAM;YAAM,OAAO;QAAkB;KACxD;AACH;AAEA,MAAM,UAAU,CAAC,EACf,KAAK,EACL,YAAY,MAAM,EAClB,QAAQ,EAAE,EAKX;IACC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBACP,GAAG,cAAc,SAAS;oBAAC;oBAAG,CAAC;iBAAK,GAAG;oBAAC,CAAC;oBAAM;iBAAE;YACnD;YACA,YAAY;gBACV,GAAG;oBACD,QAAQ;oBACR,YAAY;oBACZ,UAAU;oBACV,MAAM;gBACR;YACF;sBAGC;mBAAI;mBAAU;aAAM,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAK;;sCAE1B,8OAAC;4BAAK,WAAU;sCAAY,KAAK,IAAI;;;;;;sCACrC,8OAAC;4BAAK,WAAW,CAAC,YAAY,EAAE,KAAK,KAAK,EAAE;sCAAG,KAAK,IAAI;;;;;;;mBALnD,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;AAWxC;AAEO,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS,SAAS;wBAAE,SAAS;oBAAE,IAAI,CAAC;oBACpC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAGxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CAGxE,8OAAC;oCAAQ,OAAO,WAAW,QAAQ;oCAAE,WAAU;oCAAO,OAAO;;;;;;;;;;;;sCAI/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CAGxE,8OAAC;oCAAQ,OAAO,WAAW,OAAO;oCAAE,WAAU;oCAAQ,OAAO;;;;;;;;;;;;sCAI/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CAGxE,8OAAC;oCAAQ,OAAO,WAAW,KAAK;oCAAE,WAAU;oCAAO,OAAO;;;;;;;;;;;;;;;;;;8BAK9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAEvC;wBACC;4BAAE,QAAQ;4BAAM,OAAO;wBAAmB;wBAC1C;4BAAE,QAAQ;4BAAO,OAAO;wBAAqB;wBAC7C;4BAAE,QAAQ;4BAAO,OAAO;wBAAgB;wBACxC;4BAAE,QAAQ;4BAAO,OAAO;wBAAsB;qBAC/C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI,CAAC;4BAC9C,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM,QAAQ;4BAAI;;8CAEtD,8OAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM;;;;;;8CAEd,8OAAC;oCAAI,WAAU;8CAAiC,KAAK,KAAK;;;;;;;2BATrD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;AAgB7B", "debugId": null}}, {"offset": {"line": 2193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/services.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { \n  Code, \n  Palette, \n  Smartphone, \n  Database, \n  Cloud, \n  Zap,\n  ArrowRight\n} from \"lucide-react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\n\nconst services = [\n  {\n    icon: Code,\n    title: \"Full Stack Development\",\n    description: \"End-to-end web application development using modern technologies like React, Next.js, Node.js, and TypeScript.\",\n    features: [\"Custom Web Applications\", \"API Development\", \"Database Design\", \"Performance Optimization\"],\n    color: \"text-blue-500\",\n    bgColor: \"bg-blue-500/10\",\n  },\n  {\n    icon: Palette,\n    title: \"UI/UX Design\",\n    description: \"Creating beautiful, intuitive user interfaces and experiences that delight users and drive engagement.\",\n    features: [\"User Interface Design\", \"User Experience Research\", \"Prototyping\", \"Design Systems\"],\n    color: \"text-purple-500\",\n    bgColor: \"bg-purple-500/10\",\n  },\n  {\n    icon: Smartphone,\n    title: \"Mobile Development\",\n    description: \"Building responsive web applications and mobile-first experiences that work seamlessly across all devices.\",\n    features: [\"Responsive Design\", \"Progressive Web Apps\", \"Mobile Optimization\", \"Cross-platform Solutions\"],\n    color: \"text-green-500\",\n    bgColor: \"bg-green-500/10\",\n  },\n  {\n    icon: Database,\n    title: \"Backend Solutions\",\n    description: \"Robust backend systems, APIs, and database architectures that scale with your business needs.\",\n    features: [\"RESTful APIs\", \"Database Architecture\", \"Authentication Systems\", \"Third-party Integrations\"],\n    color: \"text-orange-500\",\n    bgColor: \"bg-orange-500/10\",\n  },\n  {\n    icon: Cloud,\n    title: \"Cloud & DevOps\",\n    description: \"Deployment, hosting, and DevOps solutions to ensure your applications are fast, secure, and reliable.\",\n    features: [\"Cloud Deployment\", \"CI/CD Pipelines\", \"Performance Monitoring\", \"Security Implementation\"],\n    color: \"text-cyan-500\",\n    bgColor: \"bg-cyan-500/10\",\n  },\n  {\n    icon: Zap,\n    title: \"Performance Optimization\",\n    description: \"Making your applications lightning-fast with advanced optimization techniques and best practices.\",\n    features: [\"Speed Optimization\", \"SEO Enhancement\", \"Code Splitting\", \"Caching Strategies\"],\n    color: \"text-yellow-500\",\n    bgColor: \"bg-yellow-500/10\",\n  },\n];\n\nexport function Services() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section ref={ref} className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Services I Offer</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            From concept to deployment, I provide comprehensive development services \n            to bring your digital vision to life.\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => {\n            const Icon = service.icon;\n            return (\n              <motion.div\n                key={service.title}\n                initial={{ opacity: 0, y: 50 }}\n                animate={inView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.8, delay: index * 0.1 }}\n              >\n                <Card className=\"h-full hover-lift group cursor-pointer border-border/50 hover:border-border transition-all duration-300\">\n                  <CardHeader>\n                    <div className={`w-12 h-12 rounded-lg ${service.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                      <Icon className={`h-6 w-6 ${service.color}`} />\n                    </div>\n                    <CardTitle className=\"text-xl font-bold group-hover:text-primary transition-colors duration-300\">\n                      {service.title}\n                    </CardTitle>\n                    <CardDescription className=\"text-muted-foreground\">\n                      {service.description}\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <ul className=\"space-y-2 mb-6\">\n                      {service.features.map((feature, featureIndex) => (\n                        <li key={featureIndex} className=\"flex items-center text-sm text-muted-foreground\">\n                          <div className={`w-1.5 h-1.5 rounded-full ${service.color.replace('text-', 'bg-')} mr-3`} />\n                          {feature}\n                        </li>\n                      ))}\n                    </ul>\n                    <Button \n                      variant=\"ghost\" \n                      className=\"w-full group-hover:bg-accent transition-colors duration-300\"\n                    >\n                      Learn More\n                      <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                    </Button>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* CTA Section */}\n        <motion.div\n          className=\"text-center mt-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.6 }}\n        >\n          <div className=\"bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-border/50\">\n            <h3 className=\"text-2xl font-bold mb-4\">Ready to Start Your Project?</h3>\n            <p className=\"text-muted-foreground mb-6 max-w-2xl mx-auto\">\n              Let's discuss how I can help bring your ideas to life with modern, \n              scalable, and user-friendly solutions.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" className=\"group\">\n                Get Started\n                <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n              </Button>\n              <Button variant=\"outline\" size=\"lg\">\n                View Portfolio\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAdA;;;;;;;AAgBA,MAAM,WAAW;IACf;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAA2B;YAAmB;YAAmB;SAA2B;QACvG,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAyB;YAA4B;YAAe;SAAiB;QAChG,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,8MAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAqB;YAAwB;YAAuB;SAA2B;QAC1G,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAgB;YAAyB;YAA0B;SAA2B;QACzG,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAoB;YAAmB;YAA0B;SAA0B;QACtG,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAsB;YAAmB;YAAkB;SAAqB;QAC3F,OAAO;QACP,SAAS;IACX;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,OAAO,QAAQ,IAAI;wBACzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,QAAQ,OAAO,CAAC,8FAA8F,CAAC;0DACrJ,cAAA,8OAAC;oDAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;;;;;;0DAE7C,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,QAAQ,KAAK;;;;;;0DAEhB,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;kDAGxB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC;gEAAI,WAAW,CAAC,yBAAyB,EAAE,QAAQ,KAAK,CAAC,OAAO,CAAC,SAAS,OAAO,KAAK,CAAC;;;;;;4DACvF;;uDAFM;;;;;;;;;;0DAMb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;2BA/BvB,QAAQ,KAAK;;;;;oBAqCxB;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;;4CAAQ;0DAElC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD", "debugId": null}}, {"offset": {"line": 2669, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/projects-showcase.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { ExternalLink, Github, ArrowRight, Filter } from \"lucide-react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\n\nconst projects = [\n  {\n    id: 1,\n    title: \"E-Commerce Platform\",\n    description: \"A modern e-commerce platform built with Next.js, featuring real-time inventory, payment processing, and admin dashboard.\",\n    image: \"/project-1.jpg\",\n    category: \"Full Stack\",\n    technologies: [\"Next.js\", \"TypeScript\", \"Prisma\", \"PostgreSQL\", \"Stripe\"],\n    liveUrl: \"https://example.com\",\n    githubUrl: \"https://github.com/ashishkamat/ecommerce\",\n    featured: true,\n  },\n  {\n    id: 2,\n    title: \"Task Management App\",\n    description: \"A collaborative task management application with real-time updates, team collaboration, and project tracking.\",\n    image: \"/project-2.jpg\",\n    category: \"Frontend\",\n    technologies: [\"React\", \"TypeScript\", \"Zustand\", \"Socket.io\", \"Tailwind\"],\n    liveUrl: \"https://example.com\",\n    githubUrl: \"https://github.com/ashishkamat/taskmanager\",\n    featured: true,\n  },\n  {\n    id: 3,\n    title: \"AI Content Generator\",\n    description: \"An AI-powered content generation tool that helps create blog posts, social media content, and marketing copy.\",\n    image: \"/project-3.jpg\",\n    category: \"AI/ML\",\n    technologies: [\"Next.js\", \"OpenAI API\", \"Prisma\", \"Supabase\", \"Framer Motion\"],\n    liveUrl: \"https://example.com\",\n    githubUrl: \"https://github.com/ashishkamat/ai-content\",\n    featured: false,\n  },\n  {\n    id: 4,\n    title: \"Real Estate Platform\",\n    description: \"A comprehensive real estate platform with property listings, virtual tours, and mortgage calculator.\",\n    image: \"/project-4.jpg\",\n    category: \"Full Stack\",\n    technologies: [\"Next.js\", \"MongoDB\", \"Cloudinary\", \"MapBox\", \"Stripe\"],\n    liveUrl: \"https://example.com\",\n    githubUrl: \"https://github.com/ashishkamat/realestate\",\n    featured: false,\n  },\n  {\n    id: 5,\n    title: \"Social Media Dashboard\",\n    description: \"A comprehensive social media management dashboard with analytics, scheduling, and content management.\",\n    image: \"/project-5.jpg\",\n    category: \"Frontend\",\n    technologies: [\"React\", \"Chart.js\", \"Redux\", \"Material-UI\", \"Node.js\"],\n    liveUrl: \"https://example.com\",\n    githubUrl: \"https://github.com/ashishkamat/social-dashboard\",\n    featured: false,\n  },\n  {\n    id: 6,\n    title: \"Fitness Tracking App\",\n    description: \"A mobile-first fitness tracking application with workout plans, progress tracking, and social features.\",\n    image: \"/project-6.jpg\",\n    category: \"Mobile\",\n    technologies: [\"React Native\", \"Expo\", \"Firebase\", \"Redux\", \"Chart.js\"],\n    liveUrl: \"https://example.com\",\n    githubUrl: \"https://github.com/ashishkamat/fitness-tracker\",\n    featured: false,\n  },\n];\n\nconst categories = [\"All\", \"Full Stack\", \"Frontend\", \"Mobile\", \"AI/ML\"];\n\nexport function ProjectsShowcase() {\n  const [selectedCategory, setSelectedCategory] = useState(\"All\");\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const filteredProjects = selectedCategory === \"All\" \n    ? projects \n    : projects.filter(project => project.category === selectedCategory);\n\n  const featuredProjects = projects.filter(project => project.featured);\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Featured Projects</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            A showcase of my recent work, featuring modern web applications \n            built with cutting-edge technologies.\n          </p>\n        </motion.div>\n\n        {/* Featured Projects */}\n        <div className=\"grid lg:grid-cols-2 gap-8 mb-16\">\n          {featuredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, y: 50 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n            >\n              <Card className=\"overflow-hidden hover-lift group border-border/50 hover:border-border transition-all duration-300\">\n                <div className=\"relative h-64 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20\" />\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"text-6xl opacity-20\">🚀</div>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <Badge variant=\"secondary\" className=\"bg-background/80 backdrop-blur-sm\">\n                      {project.category}\n                    </Badge>\n                  </div>\n                </div>\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300\">\n                    {project.title}\n                  </h3>\n                  <p className=\"text-muted-foreground mb-4 line-clamp-2\">\n                    {project.description}\n                  </p>\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {project.technologies.slice(0, 3).map((tech) => (\n                      <Badge key={tech} variant=\"outline\" className=\"text-xs\">\n                        {tech}\n                      </Badge>\n                    ))}\n                    {project.technologies.length > 3 && (\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        +{project.technologies.length - 3} more\n                      </Badge>\n                    )}\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <Button size=\"sm\" className=\"flex-1 group\">\n                      <ExternalLink className=\"mr-2 h-4 w-4 group-hover:scale-110 transition-transform duration-300\" />\n                      Live Demo\n                    </Button>\n                    <Button variant=\"outline\" size=\"sm\">\n                      <Github className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Category Filter */}\n        <motion.div\n          className=\"flex flex-wrap justify-center gap-2 mb-12\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.4 }}\n        >\n          <Filter className=\"h-5 w-5 text-muted-foreground mr-2 mt-2\" />\n          {categories.map((category) => (\n            <Button\n              key={category}\n              variant={selectedCategory === category ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={() => setSelectedCategory(category)}\n              className=\"transition-all duration-300\"\n            >\n              {category}\n            </Button>\n          ))}\n        </motion.div>\n\n        {/* All Projects Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={inView ? { opacity: 1, scale: 1 } : {}}\n              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}\n              layout\n            >\n              <Card className=\"overflow-hidden hover-lift group border-border/50 hover:border-border transition-all duration-300\">\n                <div className=\"relative h-48 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10\" />\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"text-4xl opacity-20\">💻</div>\n                  </div>\n                  <div className=\"absolute top-3 right-3\">\n                    <Badge variant=\"secondary\" className=\"text-xs bg-background/80 backdrop-blur-sm\">\n                      {project.category}\n                    </Badge>\n                  </div>\n                </div>\n                <CardContent className=\"p-4\">\n                  <h3 className=\"font-bold mb-2 group-hover:text-primary transition-colors duration-300\">\n                    {project.title}\n                  </h3>\n                  <p className=\"text-sm text-muted-foreground mb-3 line-clamp-2\">\n                    {project.description}\n                  </p>\n                  <div className=\"flex flex-wrap gap-1 mb-3\">\n                    {project.technologies.slice(0, 2).map((tech) => (\n                      <Badge key={tech} variant=\"outline\" className=\"text-xs\">\n                        {tech}\n                      </Badge>\n                    ))}\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <Button size=\"sm\" variant=\"outline\" className=\"flex-1 text-xs\">\n                      <ExternalLink className=\"mr-1 h-3 w-3\" />\n                      Demo\n                    </Button>\n                    <Button variant=\"outline\" size=\"sm\">\n                      <Github className=\"h-3 w-3\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* View All Projects CTA */}\n        <motion.div\n          className=\"text-center mt-12\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n        >\n          <Button asChild size=\"lg\" className=\"group\">\n            <Link href=\"/projects\">\n              View All Projects\n              <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n            </Link>\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAVA;;;;;;;;;;AAYA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAW;YAAc;YAAU;YAAc;SAAS;QACzE,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAS;YAAc;YAAW;YAAa;SAAW;QACzE,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAW;YAAc;YAAU;YAAY;SAAgB;QAC9E,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAW;YAAW;YAAc;YAAU;SAAS;QACtE,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAS;YAAY;YAAS;YAAe;SAAU;QACtE,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAgB;YAAQ;YAAY;YAAS;SAAW;QACvE,SAAS;QACT,WAAW;QACX,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAc;IAAY;IAAU;CAAQ;AAEhE,SAAS;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,mBAAmB,qBAAqB,QAC1C,WACA,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEpD,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IAEpE,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAOjE,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;kDAIvB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC,iIAAA,CAAA,QAAK;4DAAY,SAAQ;4DAAU,WAAU;sEAC3C;2DADS;;;;;oDAIb,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;4DAAU;4DACzC,QAAQ,YAAY,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,WAAU;;0EAC1B,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAyE;;;;;;;kEAGnG,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;kEAC7B,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA1CrB,QAAQ,EAAE;;;;;;;;;;8BAoDrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACjB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAS,qBAAqB,WAAW,YAAY;gCACrD,MAAK;gCACL,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAET;+BANI;;;;;;;;;;;8BAYX,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI,CAAC;4BAC9C,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM,QAAQ;4BAAI;4BACtD,MAAM;sCAEN,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;kDAIvB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC,iIAAA,CAAA,QAAK;wDAAY,SAAQ;wDAAU,WAAU;kEAC3C;uDADS;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;wDAAU,WAAU;;0EAC5C,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAG3C,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;kEAC7B,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtCrB,QAAQ,EAAE;;;;;;;;;;8BAgDrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,WAAU;kCAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAY;8CAErB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 3309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/testimonials.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Image from \"next/image\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { ChevronLeft, ChevronRight, Star, Quote } from \"lucide-react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\n\nconst testimonials = [\n  {\n    id: 1,\n    name: \"<PERSON>\",\n    role: \"CEO, TechStart Inc.\",\n    company: \"TechStart Inc.\",\n    image: \"/testimonial-1.jpg\",\n    content: \"<PERSON><PERSON> delivered an exceptional e-commerce platform that exceeded our expectations. His attention to detail and technical expertise helped us launch ahead of schedule and under budget.\",\n    rating: 5,\n  },\n  {\n    id: 2,\n    name: \"<PERSON>\",\n    role: \"Product Manager\",\n    company: \"InnovateLab\",\n    image: \"/testimonial-2.jpg\",\n    content: \"Working with <PERSON><PERSON> was a game-changer for our project. His full-stack expertise and modern approach to development resulted in a scalable, maintainable solution.\",\n    rating: 5,\n  },\n  {\n    id: 3,\n    name: \"<PERSON>\",\n    role: \"Startup Founder\",\n    company: \"GrowthCo\",\n    image: \"/testimonial-3.jpg\",\n    content: \"Ashish transformed our vision into reality with a beautiful, responsive web application. His communication throughout the project was excellent, and he delivered on time.\",\n    rating: 5,\n  },\n  {\n    id: 4,\n    name: \"David Thompson\",\n    role: \"CTO\",\n    company: \"DataFlow Systems\",\n    image: \"/testimonial-4.jpg\",\n    content: \"The quality of code and architecture Ashish provided was outstanding. He not only built what we asked for but also suggested improvements that enhanced the overall solution.\",\n    rating: 5,\n  },\n  {\n    id: 5,\n    name: \"Lisa Wang\",\n    role: \"Marketing Director\",\n    company: \"BrandBoost\",\n    image: \"/testimonial-5.jpg\",\n    content: \"Ashish created a stunning portfolio website that perfectly represents our brand. The performance and SEO optimization have significantly improved our online presence.\",\n    rating: 5,\n  },\n];\n\nexport function Testimonials() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n  };\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);\n  };\n\n  // Auto-advance testimonials\n  useEffect(() => {\n    const interval = setInterval(nextTestimonial, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const currentTestimonial = testimonials[currentIndex];\n\n  return (\n    <section ref={ref} className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">What Clients Say</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Don't just take my word for it. Here's what some of my clients have to say \n            about working with me.\n          </p>\n        </motion.div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Main Testimonial Card */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={inView ? { opacity: 1, scale: 1 } : {}}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <Card className=\"relative overflow-hidden border-border/50 bg-gradient-to-br from-background to-muted/30\">\n              <div className=\"absolute top-6 left-6 text-primary/20\">\n                <Quote className=\"h-12 w-12\" />\n              </div>\n              \n              <CardContent className=\"p-8 md:p-12\">\n                <AnimatePresence mode=\"wait\">\n                  <motion.div\n                    key={currentIndex}\n                    initial={{ opacity: 0, x: 50 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: -50 }}\n                    transition={{ duration: 0.5 }}\n                    className=\"text-center\"\n                  >\n                    {/* Rating Stars */}\n                    <div className=\"flex justify-center mb-6\">\n                      {[...Array(currentTestimonial.rating)].map((_, i) => (\n                        <Star key={i} className=\"h-5 w-5 fill-yellow-400 text-yellow-400\" />\n                      ))}\n                    </div>\n\n                    {/* Testimonial Content */}\n                    <blockquote className=\"text-lg md:text-xl text-foreground/90 mb-8 leading-relaxed\">\n                      \"{currentTestimonial.content}\"\n                    </blockquote>\n\n                    {/* Client Info */}\n                    <div className=\"flex items-center justify-center space-x-4\">\n                      <Avatar className=\"h-16 w-16 border-2 border-border\">\n                        <AvatarImage src={currentTestimonial.image} alt={currentTestimonial.name} />\n                        <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold\">\n                          {currentTestimonial.name.split(' ').map(n => n[0]).join('')}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div className=\"text-left\">\n                        <div className=\"font-semibold text-foreground\">{currentTestimonial.name}</div>\n                        <div className=\"text-sm text-muted-foreground\">{currentTestimonial.role}</div>\n                        <div className=\"text-sm text-primary\">{currentTestimonial.company}</div>\n                      </div>\n                    </div>\n                  </motion.div>\n                </AnimatePresence>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Navigation Controls */}\n          <motion.div\n            className=\"flex items-center justify-center space-x-4 mt-8\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={inView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <Button\n              variant=\"outline\"\n              size=\"icon\"\n              onClick={prevTestimonial}\n              className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n            </Button>\n\n            {/* Dots Indicator */}\n            <div className=\"flex space-x-2\">\n              {testimonials.map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => setCurrentIndex(index)}\n                  className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                    index === currentIndex \n                      ? 'bg-primary w-8' \n                      : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'\n                  }`}\n                />\n              ))}\n            </div>\n\n            <Button\n              variant=\"outline\"\n              size=\"icon\"\n              onClick={nextTestimonial}\n              className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n            >\n              <ChevronRight className=\"h-4 w-4\" />\n            </Button>\n          </motion.div>\n\n          {/* Testimonial Grid Preview */}\n          <motion.div\n            className=\"grid grid-cols-2 md:grid-cols-5 gap-4 mt-12\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={inView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.6 }}\n          >\n            {testimonials.map((testimonial, index) => (\n              <motion.button\n                key={testimonial.id}\n                onClick={() => setCurrentIndex(index)}\n                className={`p-3 rounded-lg border transition-all duration-300 ${\n                  index === currentIndex\n                    ? 'border-primary bg-primary/10 scale-105'\n                    : 'border-border hover:border-border/80 hover:bg-accent'\n                }`}\n                whileHover={{ scale: index === currentIndex ? 1.05 : 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <Avatar className=\"h-12 w-12 mx-auto mb-2\">\n                  <AvatarImage src={testimonial.image} alt={testimonial.name} />\n                  <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-600 text-white text-sm\">\n                    {testimonial.name.split(' ').map(n => n[0]).join('')}\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"text-xs font-medium text-center\">{testimonial.name}</div>\n                <div className=\"text-xs text-muted-foreground text-center\">{testimonial.company}</div>\n              </motion.button>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;AAWA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,QAAQ;IACV;CACD;AAEM,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;IAC5D;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IAClF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY,iBAAiB;QAC9C,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI,CAAC;4BAC9C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAGnB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC,yLAAA,CAAA,kBAAe;4CAAC,MAAK;sDACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;;kEAGV,8OAAC;wDAAI,WAAU;kEACZ;+DAAI,MAAM,mBAAmB,MAAM;yDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC7C,8OAAC,kMAAA,CAAA,OAAI;gEAAS,WAAU;+DAAb;;;;;;;;;;kEAKf,8OAAC;wDAAW,WAAU;;4DAA6D;4DAC/E,mBAAmB,OAAO;4DAAC;;;;;;;kEAI/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,8OAAC,kIAAA,CAAA,cAAW;wEAAC,KAAK,mBAAmB,KAAK;wEAAE,KAAK,mBAAmB,IAAI;;;;;;kFACxE,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,mBAAmB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;0EAG5D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAiC,mBAAmB,IAAI;;;;;;kFACvE,8OAAC;wEAAI,WAAU;kFAAiC,mBAAmB,IAAI;;;;;;kFACvE,8OAAC;wEAAI,WAAU;kFAAwB,mBAAmB,OAAO;;;;;;;;;;;;;;;;;;;+CA9BhE;;;;;;;;;;;;;;;;;;;;;;;;;;sCAwCf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAIzB,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;4CAEC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,mBACA,uDACJ;2CANG;;;;;;;;;;8CAWX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAEvC,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,kDAAkD,EAC5D,UAAU,eACN,2CACA,wDACJ;oCACF,YAAY;wCAAE,OAAO,UAAU,eAAe,OAAO;oCAAK;oCAC1D,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,YAAY,KAAK;oDAAE,KAAK,YAAY,IAAI;;;;;;8DAC1D,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;sDAGrD,8OAAC;4CAAI,WAAU;sDAAmC,YAAY,IAAI;;;;;;sDAClE,8OAAC;4CAAI,WAAU;sDAA6C,YAAY,OAAO;;;;;;;mCAjB1E,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBnC", "debugId": null}}, {"offset": {"line": 3846, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/contact-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport * as z from \"zod\";\nimport { \n  Mail, \n  Phone, \n  MapPin, \n  Send, \n  Calendar,\n  Clock,\n  Globe,\n  Linkedin,\n  Github,\n  Twitter\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { toast } from \"sonner\";\n\nconst contactSchema = z.object({\n  name: z.string().min(2, \"Name must be at least 2 characters\"),\n  email: z.string().email(\"Please enter a valid email address\"),\n  subject: z.string().min(5, \"Subject must be at least 5 characters\"),\n  message: z.string().min(10, \"Message must be at least 10 characters\"),\n});\n\ntype ContactForm = z.infer<typeof contactSchema>;\n\nconst contactInfo = [\n  {\n    icon: Mail,\n    label: \"Email\",\n    value: \"<EMAIL>\",\n    href: \"mailto:<EMAIL>\",\n  },\n  {\n    icon: Phone,\n    label: \"Phone\",\n    value: \"+****************\",\n    href: \"tel:+15551234567\",\n  },\n  {\n    icon: MapPin,\n    label: \"Location\",\n    value: \"Mumbai, India\",\n    href: \"https://maps.google.com/?q=Mumbai,India\",\n  },\n  {\n    icon: Clock,\n    label: \"Timezone\",\n    value: \"IST (UTC+5:30)\",\n    href: null,\n  },\n];\n\nconst socialLinks = [\n  {\n    icon: Linkedin,\n    label: \"LinkedIn\",\n    href: \"https://linkedin.com/in/ashishkamat\",\n    color: \"text-blue-600 hover:text-blue-700\",\n  },\n  {\n    icon: Github,\n    label: \"GitHub\",\n    href: \"https://github.com/ashishkamat\",\n    color: \"text-gray-800 dark:text-gray-200 hover:text-gray-600 dark:hover:text-gray-400\",\n  },\n  {\n    icon: Twitter,\n    label: \"Twitter\",\n    href: \"https://twitter.com/ashishkamat\",\n    color: \"text-blue-500 hover:text-blue-600\",\n  },\n  {\n    icon: Globe,\n    label: \"Website\",\n    href: \"https://ashishkamat.dev\",\n    color: \"text-green-600 hover:text-green-700\",\n  },\n];\n\nexport function ContactSection() {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const {\n    register,\n    handleSubmit,\n    reset,\n    formState: { errors },\n  } = useForm<ContactForm>({\n    resolver: zodResolver(contactSchema),\n  });\n\n  const onSubmit = async (data: ContactForm) => {\n    setIsSubmitting(true);\n    \n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      console.log(\"Form submitted:\", data);\n      toast.success(\"Message sent successfully! I'll get back to you soon.\");\n      reset();\n    } catch (error) {\n      toast.error(\"Failed to send message. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Let's Work Together</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Have a project in mind? I'd love to hear about it. \n            Let's discuss how we can bring your ideas to life.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto\">\n          {/* Contact Information */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <div>\n              <h3 className=\"text-2xl font-bold mb-6\">Get in Touch</h3>\n              <p className=\"text-muted-foreground mb-8\">\n                I'm always open to discussing new opportunities, creative projects, \n                or potential collaborations. Feel free to reach out!\n              </p>\n            </div>\n\n            {/* Contact Info Cards */}\n            <div className=\"grid sm:grid-cols-2 gap-4\">\n              {contactInfo.map((info, index) => {\n                const Icon = info.icon;\n                const content = (\n                  <Card className=\"hover-lift transition-all duration-300 border-border/50 hover:border-border\">\n                    <CardContent className=\"p-4\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center\">\n                          <Icon className=\"h-5 w-5 text-primary\" />\n                        </div>\n                        <div>\n                          <div className=\"text-sm text-muted-foreground\">{info.label}</div>\n                          <div className=\"font-medium\">{info.value}</div>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                );\n\n                return (\n                  <motion.div\n                    key={info.label}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={inView ? { opacity: 1, y: 0 } : {}}\n                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}\n                  >\n                    {info.href ? (\n                      <a href={info.href} target=\"_blank\" rel=\"noopener noreferrer\">\n                        {content}\n                      </a>\n                    ) : (\n                      content\n                    )}\n                  </motion.div>\n                );\n              })}\n            </div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: 0.6 }}\n            >\n              <h4 className=\"font-semibold mb-4\">Connect with me</h4>\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social, index) => {\n                  const Icon = social.icon;\n                  return (\n                    <motion.a\n                      key={social.label}\n                      href={social.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className={`p-3 rounded-full bg-background border border-border hover:border-primary/50 transition-all duration-300 ${social.color}`}\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      initial={{ opacity: 0, scale: 0 }}\n                      animate={inView ? { opacity: 1, scale: 1 } : {}}\n                      transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}\n                    >\n                      <Icon className=\"h-5 w-5\" />\n                    </motion.a>\n                  );\n                })}\n              </div>\n            </motion.div>\n\n            {/* Availability */}\n            <motion.div\n              className=\"bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-6 border border-border/50\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: 0.8 }}\n            >\n              <div className=\"flex items-center space-x-3 mb-2\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\" />\n                <span className=\"font-semibold text-green-700 dark:text-green-400\">Available for new projects</span>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">\n                I'm currently accepting new client work and interesting project collaborations.\n              </p>\n            </motion.div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <Card className=\"border-border/50\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Send className=\"h-5 w-5\" />\n                  <span>Send me a message</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid sm:grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"name\">Name *</Label>\n                      <Input\n                        id=\"name\"\n                        placeholder=\"Your name\"\n                        {...register(\"name\")}\n                        className={errors.name ? \"border-destructive\" : \"\"}\n                      />\n                      {errors.name && (\n                        <p className=\"text-sm text-destructive\">{errors.name.message}</p>\n                      )}\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"email\">Email *</Label>\n                      <Input\n                        id=\"email\"\n                        type=\"email\"\n                        placeholder=\"<EMAIL>\"\n                        {...register(\"email\")}\n                        className={errors.email ? \"border-destructive\" : \"\"}\n                      />\n                      {errors.email && (\n                        <p className=\"text-sm text-destructive\">{errors.email.message}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"subject\">Subject *</Label>\n                    <Input\n                      id=\"subject\"\n                      placeholder=\"Project inquiry, collaboration, etc.\"\n                      {...register(\"subject\")}\n                      className={errors.subject ? \"border-destructive\" : \"\"}\n                    />\n                    {errors.subject && (\n                      <p className=\"text-sm text-destructive\">{errors.subject.message}</p>\n                    )}\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"message\">Message *</Label>\n                    <Textarea\n                      id=\"message\"\n                      placeholder=\"Tell me about your project, timeline, budget, and any specific requirements...\"\n                      rows={6}\n                      {...register(\"message\")}\n                      className={errors.message ? \"border-destructive\" : \"\"}\n                    />\n                    {errors.message && (\n                      <p className=\"text-sm text-destructive\">{errors.message.message}</p>\n                    )}\n                  </div>\n\n                  <Button \n                    type=\"submit\" \n                    className=\"w-full group\" \n                    disabled={isSubmitting}\n                  >\n                    {isSubmitting ? (\n                      <>\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\" />\n                        Sending...\n                      </>\n                    ) : (\n                      <>\n                        <Send className=\"mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                        Send Message\n                      </>\n                    )}\n                  </Button>\n                </form>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AAzBA;;;;;;;;;;;;;;;AA2BA,MAAM,gBAAgB,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,EAAE;IAC7B,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IACxB,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;IACxB,SAAS,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC3B,SAAS,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI;AAC9B;AAIA,MAAM,cAAc;IAClB;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,cAAc;IAClB;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,MAAM;QACN,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAe;QACvB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAO5C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;wCACtB,MAAM,OAAO,KAAK,IAAI;wCACtB,MAAM,wBACJ,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;;;;;;;;;;;sEAElB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAiC,KAAK,KAAK;;;;;;8EAC1D,8OAAC;oEAAI,WAAU;8EAAe,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAOlD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI,CAAC;4CAC1C,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;sDAErD,KAAK,IAAI,iBACR,8OAAC;gDAAE,MAAM,KAAK,IAAI;gDAAE,QAAO;gDAAS,KAAI;0DACrC;;;;;uDAGH;2CAVG,KAAK,KAAK;;;;;oCAcrB;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,wGAAwG,EAAE,OAAO,KAAK,EAAE;oDACpI,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,SAAS,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE,IAAI,CAAC;oDAC9C,YAAY;wDAAE,UAAU;wDAAK,OAAO,MAAM,QAAQ;oDAAI;8DAEtD,cAAA,8OAAC;wDAAK,WAAU;;;;;;mDAXX,OAAO,KAAK;;;;;4CAcvB;;;;;;;;;;;;8CAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAmD;;;;;;;;;;;;sDAErE,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAOjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAK,UAAU,aAAa;4CAAW,WAAU;;8DAChD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAO;;;;;;8EACtB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,aAAY;oEACX,GAAG,SAAS,OAAO;oEACpB,WAAW,OAAO,IAAI,GAAG,uBAAuB;;;;;;gEAEjD,OAAO,IAAI,kBACV,8OAAC;oEAAE,WAAU;8EAA4B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;sEAGhE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAQ;;;;;;8EACvB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,aAAY;oEACX,GAAG,SAAS,QAAQ;oEACrB,WAAW,OAAO,KAAK,GAAG,uBAAuB;;;;;;gEAElD,OAAO,KAAK,kBACX,8OAAC;oEAAE,WAAU;8EAA4B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;8DAKnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAU;;;;;;sEACzB,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,aAAY;4DACX,GAAG,SAAS,UAAU;4DACvB,WAAW,OAAO,OAAO,GAAG,uBAAuB;;;;;;wDAEpD,OAAO,OAAO,kBACb,8OAAC;4DAAE,WAAU;sEAA4B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8DAInE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAU;;;;;;sEACzB,8OAAC,oIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,aAAY;4DACZ,MAAM;4DACL,GAAG,SAAS,UAAU;4DACvB,WAAW,OAAO,OAAO,GAAG,uBAAuB;;;;;;wDAEpD,OAAO,OAAO,kBACb,8OAAC;4DAAE,WAAU;sEAA4B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8DAInE,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;oDACV,UAAU;8DAET,6BACC;;0EACE,8OAAC;gEAAI,WAAU;;;;;;4DAAmE;;qFAIpF;;0EACE,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAA6E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarH", "debugId": null}}, {"offset": {"line": 4622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { \n  Github, \n  Linkedin, \n  Twitter, \n  Mail, \n  Heart, \n  ArrowUp,\n  Code,\n  Coffee\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst footerLinks = {\n  navigation: [\n    { name: \"Home\", href: \"/\" },\n    { name: \"About\", href: \"/about\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  services: [\n    { name: \"Full Stack Development\", href: \"/#services\" },\n    { name: \"UI/UX Design\", href: \"/#services\" },\n    { name: \"Mobile Development\", href: \"/#services\" },\n    { name: \"Consulting\", href: \"/#services\" },\n  ],\n  resources: [\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Tech Stack\", href: \"/#tech-stack\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n};\n\nconst socialLinks = [\n  {\n    name: \"GitHub\",\n    href: \"https://github.com/ashishkamat\",\n    icon: Github,\n    color: \"hover:text-gray-600 dark:hover:text-gray-300\",\n  },\n  {\n    name: \"LinkedIn\",\n    href: \"https://linkedin.com/in/ashishkamat\",\n    icon: Linkedin,\n    color: \"hover:text-blue-600\",\n  },\n  {\n    name: \"Twitter\",\n    href: \"https://twitter.com/ashishkamat\",\n    icon: Twitter,\n    color: \"hover:text-blue-500\",\n  },\n  {\n    name: \"Email\",\n    href: \"mailto:<EMAIL>\",\n    icon: Mail,\n    color: \"hover:text-green-600\",\n  },\n];\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  return (\n    <footer className=\"bg-background border-t border-border\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 lg:py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n                viewport={{ once: true }}\n              >\n                <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue mb-4 inline-block\">\n                  Ashish Kamat\n                </Link>\n                <p className=\"text-muted-foreground mb-6 max-w-md\">\n                  Full Stack Developer & UI/UX Designer passionate about creating \n                  innovative digital experiences that make a difference.\n                </p>\n                \n                {/* Social Links */}\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => {\n                    const Icon = social.icon;\n                    return (\n                      <motion.a\n                        key={social.name}\n                        href={social.href}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className={`p-2 rounded-lg bg-muted hover:bg-accent transition-all duration-300 ${social.color}`}\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        initial={{ opacity: 0, scale: 0 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        transition={{ duration: 0.3, delay: index * 0.1 }}\n                        viewport={{ once: true }}\n                      >\n                        <Icon className=\"h-5 w-5\" />\n                      </motion.a>\n                    );\n                  })}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Navigation Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Navigation</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.navigation.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Services Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Services</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Resources Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <motion.div\n          className=\"py-6 border-t border-border\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n              <span>© 2024 Ashish Kamat. Made with</span>\n              <Heart className=\"h-4 w-4 text-red-500 animate-pulse\" />\n              <span>and</span>\n              <Coffee className=\"h-4 w-4 text-amber-600\" />\n              <span>in Mumbai</span>\n            </div>\n\n            {/* Tech Stack & Back to Top */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <Code className=\"h-4 w-4\" />\n                <span>Built with Next.js & Tailwind CSS</span>\n              </div>\n              \n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={scrollToTop}\n                className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n              >\n                <ArrowUp className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Fun Easter Egg */}\n        <motion.div\n          className=\"text-center py-4 border-t border-border/50\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <p className=\"text-xs text-muted-foreground/70\">\n            🚀 This website is powered by{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% coffee\n            </span>{\" \"}\n            and{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% passion\n            </span>\n          </p>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAgBA,MAAM,cAAc;IAClB,YAAY;QACV;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAA0B,MAAM;QAAa;QACrD;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAc,MAAM;QAAa;KAC1C;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAe;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA0D;;;;;;sDAGnF,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;sDAMnD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,oEAAoE,EAAE,OAAO,KAAK,EAAE;oDAChG,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,aAAa;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDACpC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;8DAEvB,cAAA,8OAAC;wDAAK,WAAU;;;;;;mDAZX,OAAO,IAAI;;;;;4CAetB;;;;;;;;;;;;;;;;;0CAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC3B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAE,WAAU;;4BAAmC;4BAChB;0CAC9B,8OAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;4BAC3B;4BAAI;4BACR;0CACJ,8OAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}