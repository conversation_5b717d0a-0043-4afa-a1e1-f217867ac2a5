"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { ExternalLink, Github, Calendar, Users, Star } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

const projects = [
  {
    id: 1,
    title: "E-Commerce Platform",
    description: "A full-featured e-commerce platform with real-time inventory management, payment processing, admin dashboard, and customer analytics. Built for scalability and performance.",
    longDescription: "This comprehensive e-commerce solution features a modern React frontend with Next.js for optimal performance, a robust Node.js backend with PostgreSQL database, and integrated payment processing through Stripe. The platform includes advanced features like real-time inventory tracking, automated email notifications, comprehensive analytics dashboard, and mobile-responsive design.",
    image: "/project-1.jpg",
    category: "Full Stack",
    technologies: ["Next.js", "TypeScript", "Prisma", "PostgreSQL", "Stripe", "Tailwind CSS", "Framer Motion"],
    liveUrl: "https://ecommerce-demo.ashishkamat.dev",
    githubUrl: "https://github.com/ashishkamat/ecommerce-platform",
    featured: true,
    status: "Completed",
    duration: "3 months",
    team: "Solo Project",
    year: "2024",
    highlights: [
      "Handles 10,000+ products with real-time inventory",
      "Integrated payment processing with Stripe",
      "Admin dashboard with comprehensive analytics",
      "Mobile-responsive design with 95+ Lighthouse score"
    ]
  },
  {
    id: 2,
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates, team collaboration features, project tracking, and advanced filtering capabilities.",
    longDescription: "This project management tool enables teams to collaborate effectively with real-time updates, drag-and-drop task management, time tracking, and comprehensive reporting. Built with React and Socket.io for real-time functionality, featuring a clean and intuitive interface designed for productivity.",
    image: "/project-2.jpg",
    category: "Frontend",
    technologies: ["React", "TypeScript", "Zustand", "Socket.io", "Tailwind CSS", "React DnD"],
    liveUrl: "https://taskmanager-demo.ashishkamat.dev",
    githubUrl: "https://github.com/ashishkamat/task-manager",
    featured: true,
    status: "Completed",
    duration: "2 months",
    team: "2 Developers",
    year: "2024",
    highlights: [
      "Real-time collaboration with Socket.io",
      "Drag-and-drop task management",
      "Advanced filtering and search capabilities",
      "Time tracking and reporting features"
    ]
  },
  {
    id: 3,
    title: "AI Content Generator",
    description: "An AI-powered content generation tool that helps create blog posts, social media content, and marketing copy using OpenAI's GPT models.",
    longDescription: "This innovative tool leverages OpenAI's powerful language models to generate high-quality content for various purposes. Features include customizable templates, tone adjustment, content optimization suggestions, and integration with popular content management systems.",
    image: "/project-3.jpg",
    category: "AI/ML",
    technologies: ["Next.js", "OpenAI API", "Prisma", "Supabase", "Framer Motion", "React Hook Form"],
    liveUrl: "https://ai-content-demo.ashishkamat.dev",
    githubUrl: "https://github.com/ashishkamat/ai-content-generator",
    featured: false,
    status: "In Progress",
    duration: "4 months",
    team: "Solo Project",
    year: "2024",
    highlights: [
      "Integration with OpenAI GPT-4 API",
      "Multiple content templates and formats",
      "Real-time content optimization suggestions",
      "Export to various formats (PDF, Word, HTML)"
    ]
  },
  {
    id: 4,
    title: "Real Estate Platform",
    description: "A comprehensive real estate platform with property listings, virtual tours, mortgage calculator, and advanced search filters.",
    longDescription: "This real estate platform provides a complete solution for property buyers, sellers, and agents. Features include interactive maps, virtual property tours, mortgage calculations, and advanced search capabilities with multiple filters for location, price, and property features.",
    image: "/project-4.jpg",
    category: "Full Stack",
    technologies: ["Next.js", "MongoDB", "Cloudinary", "MapBox", "Stripe", "React Query"],
    liveUrl: "https://realestate-demo.ashishkamat.dev",
    githubUrl: "https://github.com/ashishkamat/real-estate-platform",
    featured: false,
    status: "Completed",
    duration: "5 months",
    team: "3 Developers",
    year: "2023",
    highlights: [
      "Interactive maps with MapBox integration",
      "Virtual property tours with 360° images",
      "Advanced search with multiple filters",
      "Integrated mortgage calculator"
    ]
  },
  {
    id: 5,
    title: "Social Media Dashboard",
    description: "A comprehensive social media management dashboard with analytics, scheduling, content management, and performance tracking.",
    longDescription: "This dashboard provides social media managers with powerful tools to manage multiple platforms, schedule content, track performance metrics, and analyze engagement patterns. Built with modern React patterns and integrated with major social media APIs.",
    image: "/project-5.jpg",
    category: "Frontend",
    technologies: ["React", "Chart.js", "Redux Toolkit", "Material-UI", "Node.js", "Express"],
    liveUrl: "https://social-dashboard-demo.ashishkamat.dev",
    githubUrl: "https://github.com/ashishkamat/social-media-dashboard",
    featured: false,
    status: "Completed",
    duration: "3 months",
    team: "Solo Project",
    year: "2023",
    highlights: [
      "Multi-platform social media integration",
      "Advanced analytics and reporting",
      "Content scheduling and automation",
      "Real-time engagement tracking"
    ]
  },
  {
    id: 6,
    title: "Fitness Tracking App",
    description: "A mobile-first fitness tracking application with workout plans, progress tracking, social features, and nutrition logging.",
    longDescription: "This comprehensive fitness app helps users track their workouts, monitor progress, follow structured workout plans, and connect with other fitness enthusiasts. Features include exercise libraries, progress photos, nutrition tracking, and social challenges.",
    image: "/project-6.jpg",
    category: "Mobile",
    technologies: ["React Native", "Expo", "Firebase", "Redux", "Chart.js", "AsyncStorage"],
    liveUrl: "https://fitness-app-demo.ashishkamat.dev",
    githubUrl: "https://github.com/ashishkamat/fitness-tracker",
    featured: false,
    status: "Completed",
    duration: "4 months",
    team: "2 Developers",
    year: "2023",
    highlights: [
      "Comprehensive exercise library with videos",
      "Progress tracking with photos and measurements",
      "Social features and challenges",
      "Nutrition logging and meal planning"
    ]
  }
];

const categories = ["All", "Full Stack", "Frontend", "Mobile", "AI/ML"];

export function ProjectsGrid() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const filteredProjects = selectedCategory === "All" 
    ? projects 
    : projects.filter(project => project.category === selectedCategory);

  return (
    <section ref={ref} className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">All Projects</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Explore my complete portfolio of projects, each demonstrating different aspects 
            of modern web development and innovative problem-solving.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-2 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="transition-all duration-300"
            >
              {category}
            </Button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={inView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
              layout
            >
              <Card className="h-full hover-lift group border-border/50 hover:border-border transition-all duration-300">
                <div className="relative h-48 overflow-hidden rounded-t-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-6xl opacity-20">
                      {project.category === "AI/ML" ? "🤖" : 
                       project.category === "Mobile" ? "📱" : 
                       project.category === "Frontend" ? "🎨" : "💻"}
                    </div>
                  </div>
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary" className="bg-background/80 backdrop-blur-sm">
                      {project.category}
                    </Badge>
                  </div>
                  <div className="absolute top-4 right-4">
                    <Badge 
                      variant={project.status === "Completed" ? "default" : "secondary"}
                      className="bg-background/80 backdrop-blur-sm text-xs"
                    >
                      {project.status}
                    </Badge>
                  </div>
                  {project.featured && (
                    <div className="absolute bottom-4 left-4">
                      <Badge className="bg-yellow-500 text-yellow-900">
                        <Star className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    </div>
                  )}
                </div>
                
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors duration-300">
                    {project.title}
                  </CardTitle>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {project.description}
                  </p>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Project Meta */}
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{project.year}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-3 w-3" />
                      <span>{project.team}</span>
                    </div>
                  </div>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-1">
                    {project.technologies.slice(0, 3).map((tech) => (
                      <Badge key={tech} variant="outline" className="text-xs">
                        {tech}
                      </Badge>
                    ))}
                    {project.technologies.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{project.technologies.length - 3}
                      </Badge>
                    )}
                  </div>

                  {/* Key Highlights */}
                  <div>
                    <h4 className="text-sm font-semibold mb-2">Key Features:</h4>
                    <ul className="space-y-1">
                      {project.highlights.slice(0, 2).map((highlight, highlightIndex) => (
                        <li key={highlightIndex} className="flex items-start space-x-2 text-xs text-muted-foreground">
                          <div className="w-1 h-1 rounded-full bg-primary mt-1.5 flex-shrink-0" />
                          <span>{highlight}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" className="flex-1 group">
                      <ExternalLink className="mr-2 h-3 w-3 group-hover:scale-110 transition-transform duration-300" />
                      Live Demo
                    </Button>
                    <Button variant="outline" size="sm">
                      <Github className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
