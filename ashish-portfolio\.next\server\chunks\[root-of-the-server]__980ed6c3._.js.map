{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/app/manifest.ts"], "sourcesContent": ["import { MetadataRoute } from 'next'\n\nexport default function manifest(): MetadataRoute.Manifest {\n  return {\n    name: '<PERSON><PERSON> - Full Stack Developer',\n    short_name: '<PERSON><PERSON>',\n    description: 'Full Stack Developer & UI/UX Designer creating innovative digital experiences',\n    start_url: '/',\n    display: 'standalone',\n    background_color: '#ffffff',\n    theme_color: '#3b82f6',\n    icons: [\n      {\n        src: '/icon-192x192.png',\n        sizes: '192x192',\n        type: 'image/png',\n      },\n      {\n        src: '/icon-512x512.png',\n        sizes: '512x512',\n        type: 'image/png',\n      },\n    ],\n  }\n}\n"], "names": [], "mappings": ";;;AAEe,SAAS;IACtB,OAAO;QACL,MAAM;QACN,YAAY;QACZ,aAAa;QACb,WAAW;QACX,SAAS;QACT,kBAAkB;QAClB,aAAa;QACb,OAAO;YACL;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/app/manifest--route-entry.js"], "sourcesContent": ["            import { NextResponse } from 'next/server'\n            import handler from \"./manifest.ts\"\n            import { resolveRouteData } from\n'next/dist/build/webpack/loaders/metadata/resolve-route-data'\n\n            const contentType = \"application/manifest+json\"\n            const cacheControl = \"public, max-age=0, must-revalidate\"\n            const fileType = \"manifest\"\n\n            if (typeof handler !== 'function') {\n                throw new Error('Default export is missing in \"./manifest.ts\"')\n            }\n\n            export async function GET() {\n              const data = await handler()\n              const content = resolveRouteData(data, fileType)\n\n              return new NextResponse(content, {\n                headers: {\n                  'Content-Type': contentType,\n                  'Cache-Control': cacheControl,\n                },\n              })\n            }\n        "], "names": [], "mappings": ";;;AAAY;AACA;AACA;;;;AAGA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,WAAW;AAEjB,IAAI,OAAO,sHAAA,CAAA,UAAO,KAAK,YAAY;IAC/B,MAAM,IAAI,MAAM;AACpB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,UAAO,AAAD;IACzB,MAAM,UAAU,CAAA,GAAA,mMAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;IAEvC,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,SAAS;QAC/B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;IACF;AACF", "debugId": null}}]}