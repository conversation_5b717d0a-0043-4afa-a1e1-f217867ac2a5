"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { GraduationCap, Award, BookOpen, Calendar } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const education = [
  {
    degree: "Bachelor of Engineering in Computer Science",
    institution: "Mumbai University",
    location: "Mumbai, India",
    period: "2016 - 2020",
    grade: "First Class with Distinction (8.5/10 CGPA)",
    description: "Focused on software engineering, data structures, algorithms, and web technologies. Completed final year project on e-commerce platform development.",
    highlights: [
      "Dean's List for 3 consecutive semesters",
      "Led university coding club with 200+ members",
      "Won inter-college hackathon for innovative web solution",
      "Published research paper on web performance optimization"
    ]
  },
  {
    degree: "Higher Secondary Certificate (Science)",
    institution: "St. Xavier's College",
    location: "Mumbai, India",
    period: "2014 - 2016",
    grade: "92.5%",
    description: "Specialized in Mathematics, Physics, and Chemistry with additional focus on computer science fundamentals.",
    highlights: [
      "School topper in Computer Science",
      "Represented school in state-level science exhibition",
      "Active member of robotics club"
    ]
  }
];

const certifications = [
  {
    title: "AWS Certified Solutions Architect",
    issuer: "Amazon Web Services",
    date: "2023",
    credentialId: "AWS-CSA-2023-001",
    icon: "☁️"
  },
  {
    title: "React Developer Certification",
    issuer: "Meta (Facebook)",
    date: "2022",
    credentialId: "META-REACT-2022-456",
    icon: "⚛️"
  },
  {
    title: "Google Analytics Certified",
    issuer: "Google",
    date: "2022",
    credentialId: "GOOGLE-GA-2022-789",
    icon: "📊"
  },
  {
    title: "MongoDB Developer Certification",
    issuer: "MongoDB University",
    date: "2021",
    credentialId: "MONGO-DEV-2021-123",
    icon: "🍃"
  }
];

const courses = [
  "Advanced React Patterns - Kent C. Dodds",
  "TypeScript Masterclass - Marius Schulz",
  "Node.js Design Patterns - Mario Casciaro",
  "System Design Interview - Alex Xu",
  "AWS Solutions Architecture - A Cloud Guru",
  "UI/UX Design Fundamentals - Google UX"
];

export function Education() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section ref={ref} className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Education & Learning</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            My educational background and continuous learning journey in technology and software development.
          </p>
        </motion.div>

        {/* Formal Education */}
        <div className="mb-16">
          <motion.h3
            className="text-2xl font-bold mb-8 flex items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <GraduationCap className="mr-3 h-6 w-6 text-primary" />
            Formal Education
          </motion.h3>
          
          <div className="space-y-6">
            {education.map((edu, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              >
                <Card className="hover-lift border-border/50 hover:border-border transition-all duration-300">
                  <CardHeader>
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                      <div>
                        <CardTitle className="text-xl font-bold">{edu.degree}</CardTitle>
                        <div className="text-primary font-medium">{edu.institution}</div>
                        <div className="text-sm text-muted-foreground">{edu.location}</div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center text-sm text-muted-foreground mb-1">
                          <Calendar className="h-4 w-4 mr-1" />
                          {edu.period}
                        </div>
                        <Badge variant="secondary">{edu.grade}</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">{edu.description}</p>
                    <div>
                      <h4 className="font-semibold mb-2">Key Highlights:</h4>
                      <ul className="space-y-1">
                        {edu.highlights.map((highlight, highlightIndex) => (
                          <li key={highlightIndex} className="flex items-start space-x-2 text-sm text-muted-foreground">
                            <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                            <span>{highlight}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Certifications */}
        <div className="mb-16">
          <motion.h3
            className="text-2xl font-bold mb-8 flex items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Award className="mr-3 h-6 w-6 text-primary" />
            Certifications
          </motion.h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {certifications.map((cert, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={inView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
              >
                <Card className="hover-lift border-border/50 hover:border-border transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="text-3xl">{cert.icon}</div>
                      <div className="flex-1">
                        <h4 className="font-bold">{cert.title}</h4>
                        <p className="text-primary font-medium">{cert.issuer}</p>
                        <p className="text-sm text-muted-foreground">Issued: {cert.date}</p>
                        <p className="text-xs text-muted-foreground mt-1">ID: {cert.credentialId}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Continuous Learning */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h3 className="text-2xl font-bold mb-8 flex items-center">
            <BookOpen className="mr-3 h-6 w-6 text-primary" />
            Continuous Learning
          </h3>
          
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle>Recent Courses & Training</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {courses.map((course, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors duration-200"
                    initial={{ opacity: 0, x: -20 }}
                    animate={inView ? { opacity: 1, x: 0 } : {}}
                    transition={{ duration: 0.4, delay: 0.7 + index * 0.05 }}
                  >
                    <div className="w-2 h-2 rounded-full bg-primary flex-shrink-0" />
                    <span className="text-sm font-medium">{course}</span>
                  </motion.div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-border/50">
                <p className="text-sm text-muted-foreground">
                  <strong>Learning Philosophy:</strong> I believe in continuous learning and staying updated with the latest 
                  technologies. I dedicate at least 5 hours per week to learning new skills and exploring emerging trends 
                  in web development and software engineering.
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
