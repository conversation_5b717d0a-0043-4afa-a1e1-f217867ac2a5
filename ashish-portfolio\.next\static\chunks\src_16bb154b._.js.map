{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/command-palette.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { \n  Search, \n  Home, \n  User, \n  Briefcase, \n  Mail, \n  FileText,\n  ExternalLink,\n  Github,\n  Linkedin,\n  Twitter\n} from \"lucide-react\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface Command {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<any>;\n  action: () => void;\n  category: string;\n  keywords: string[];\n}\n\ninterface CommandPaletteProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {\n  const [search, setSearch] = useState(\"\");\n  const [selectedIndex, setSelectedIndex] = useState(0);\n  const router = useRouter();\n\n  const commands: Command[] = [\n    // Navigation\n    {\n      id: \"home\",\n      title: \"Home\",\n      description: \"Go to homepage\",\n      icon: Home,\n      action: () => router.push(\"/\"),\n      category: \"Navigation\",\n      keywords: [\"home\", \"main\", \"landing\"],\n    },\n    {\n      id: \"about\",\n      title: \"About\",\n      description: \"Learn more about me\",\n      icon: User,\n      action: () => router.push(\"/about\"),\n      category: \"Navigation\",\n      keywords: [\"about\", \"bio\", \"experience\", \"skills\"],\n    },\n    {\n      id: \"projects\",\n      title: \"Projects\",\n      description: \"View my work and projects\",\n      icon: Briefcase,\n      action: () => router.push(\"/projects\"),\n      category: \"Navigation\",\n      keywords: [\"projects\", \"work\", \"portfolio\", \"showcase\"],\n    },\n    {\n      id: \"blog\",\n      title: \"Blog\",\n      description: \"Read my latest articles\",\n      icon: FileText,\n      action: () => router.push(\"/blog\"),\n      category: \"Navigation\",\n      keywords: [\"blog\", \"articles\", \"writing\", \"posts\"],\n    },\n    {\n      id: \"contact\",\n      title: \"Contact\",\n      description: \"Get in touch with me\",\n      icon: Mail,\n      action: () => router.push(\"/contact\"),\n      category: \"Navigation\",\n      keywords: [\"contact\", \"email\", \"message\", \"hire\"],\n    },\n    // External Links\n    {\n      id: \"github\",\n      title: \"GitHub\",\n      description: \"View my GitHub profile\",\n      icon: Github,\n      action: () => window.open(\"https://github.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"github\", \"code\", \"repositories\", \"open source\"],\n    },\n    {\n      id: \"linkedin\",\n      title: \"LinkedIn\",\n      description: \"Connect with me on LinkedIn\",\n      icon: Linkedin,\n      action: () => window.open(\"https://linkedin.com/in/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"linkedin\", \"professional\", \"network\", \"career\"],\n    },\n    {\n      id: \"twitter\",\n      title: \"Twitter\",\n      description: \"Follow me on Twitter\",\n      icon: Twitter,\n      action: () => window.open(\"https://twitter.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"twitter\", \"social\", \"updates\", \"thoughts\"],\n    },\n    // Quick Actions\n    {\n      id: \"email\",\n      title: \"Send Email\",\n      description: \"Send me an email directly\",\n      icon: Mail,\n      action: () => window.open(\"mailto:<EMAIL>\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"email\", \"contact\", \"message\", \"hire\"],\n    },\n    {\n      id: \"resume\",\n      title: \"Download Resume\",\n      description: \"Download my latest resume\",\n      icon: ExternalLink,\n      action: () => window.open(\"/resume.pdf\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"resume\", \"cv\", \"download\", \"hire\"],\n    },\n  ];\n\n  const filteredCommands = commands.filter((command) => {\n    const searchLower = search.toLowerCase();\n    return (\n      command.title.toLowerCase().includes(searchLower) ||\n      command.description.toLowerCase().includes(searchLower) ||\n      command.keywords.some((keyword) => keyword.includes(searchLower))\n    );\n  });\n\n  const groupedCommands = filteredCommands.reduce((acc, command) => {\n    if (!acc[command.category]) {\n      acc[command.category] = [];\n    }\n    acc[command.category].push(command);\n    return acc;\n  }, {} as Record<string, Command[]>);\n\n  useEffect(() => {\n    setSelectedIndex(0);\n  }, [search]);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open) return;\n\n      if (e.key === \"ArrowDown\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev < filteredCommands.length - 1 ? prev + 1 : 0\n        );\n      } else if (e.key === \"ArrowUp\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev > 0 ? prev - 1 : filteredCommands.length - 1\n        );\n      } else if (e.key === \"Enter\") {\n        e.preventDefault();\n        if (filteredCommands[selectedIndex]) {\n          filteredCommands[selectedIndex].action();\n          onOpenChange(false);\n          setSearch(\"\");\n        }\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [open, selectedIndex, filteredCommands, onOpenChange]);\n\n  const handleCommandSelect = (command: Command) => {\n    command.action();\n    onOpenChange(false);\n    setSearch(\"\");\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl p-0 overflow-hidden\">\n        <DialogHeader className=\"p-4 pb-0\">\n          <DialogTitle className=\"sr-only\">Command Palette</DialogTitle>\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Type a command or search...\"\n              value={search}\n              onChange={(e) => setSearch(e.target.value)}\n              className=\"pl-10 border-0 focus-visible:ring-0 text-base\"\n              autoFocus\n            />\n          </div>\n        </DialogHeader>\n\n        <div className=\"max-h-96 overflow-y-auto p-4 pt-0\">\n          {Object.keys(groupedCommands).length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No commands found for \"{search}\"\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {Object.entries(groupedCommands).map(([category, commands]) => (\n                <div key={category}>\n                  <div className=\"text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2\">\n                    {category}\n                  </div>\n                  <div className=\"space-y-1\">\n                    {commands.map((command, index) => {\n                      const globalIndex = filteredCommands.indexOf(command);\n                      const Icon = command.icon;\n                      \n                      return (\n                        <motion.button\n                          key={command.id}\n                          onClick={() => handleCommandSelect(command)}\n                          className={`w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ${\n                            globalIndex === selectedIndex\n                              ? \"bg-accent text-accent-foreground\"\n                              : \"hover:bg-accent/50\"\n                          }`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <div className={`p-2 rounded-md ${\n                            globalIndex === selectedIndex\n                              ? \"bg-primary text-primary-foreground\"\n                              : \"bg-muted\"\n                          }`}>\n                            <Icon className=\"h-4 w-4\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"font-medium\">{command.title}</div>\n                            <div className=\"text-sm text-muted-foreground truncate\">\n                              {command.description}\n                            </div>\n                          </div>\n                          {command.category === \"Social\" && (\n                            <ExternalLink className=\"h-3 w-3 text-muted-foreground\" />\n                          )}\n                        </motion.button>\n                      );\n                    })}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-t p-3 text-xs text-muted-foreground flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↑↓</Badge>\n              <span>Navigate</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↵</Badge>\n              <span>Select</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">Esc</Badge>\n              <span>Close</span>\n            </div>\n          </div>\n          <div className=\"text-muted-foreground/60\">\n            {filteredCommands.length} result{filteredCommands.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAMA;AACA;;;AAxBA;;;;;;;;AAyCO,SAAS,eAAe,EAAE,IAAI,EAAE,YAAY,EAAuB;;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,WAAsB;QAC1B,aAAa;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAQ;aAAU;QACvC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAO;gBAAc;aAAS;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,+MAAA,CAAA,YAAS;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAQ;gBAAa;aAAW;QACzD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,iNAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAY;gBAAW;aAAQ;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAS;gBAAW;aAAO;QACnD;QACA,iBAAiB;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,yMAAA,CAAA,SAAM;YACZ,QAAQ,IAAM,OAAO,IAAI,CAAC,kCAAkC;YAC5D,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAQ;gBAAgB;aAAc;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,6MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC,uCAAuC;YACjE,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAgB;gBAAW;aAAS;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,2MAAA,CAAA,UAAO;YACb,QAAQ,IAAM,OAAO,IAAI,CAAC,mCAAmC;YAC7D,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAU;gBAAW;aAAW;QACxD;QACA,gBAAgB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC,gCAAgC;YAC1D,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAW;gBAAW;aAAO;QACnD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,yNAAA,CAAA,eAAY;YAClB,QAAQ,IAAM,OAAO,IAAI,CAAC,eAAe;YACzC,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAM;gBAAY;aAAO;QAChD;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,cAAc,OAAO,WAAW;QACtC,OACE,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC3C,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,QAAQ,CAAC;IAExD;IAEA,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;QAC5B;QACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC3B,OAAO;IACT,GAAG,CAAC;IAEJ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,iBAAiB;QACnB;mCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB,CAAC;oBACrB,IAAI,CAAC,MAAM;oBAEX,IAAI,EAAE,GAAG,KAAK,aAAa;wBACzB,EAAE,cAAc;wBAChB;sEAAiB,CAAC,OAChB,OAAO,iBAAiB,MAAM,GAAG,IAAI,OAAO,IAAI;;oBAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;wBAC9B,EAAE,cAAc;wBAChB;sEAAiB,CAAC,OAChB,OAAO,IAAI,OAAO,IAAI,iBAAiB,MAAM,GAAG;;oBAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;wBAC5B,EAAE,cAAc;wBAChB,IAAI,gBAAgB,CAAC,cAAc,EAAE;4BACnC,gBAAgB,CAAC,cAAc,CAAC,MAAM;4BACtC,aAAa;4BACb,UAAU;wBACZ;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;mCAAG;QAAC;QAAM;QAAe;QAAkB;KAAa;IAExD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,MAAM;QACd,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAU;;;;;;sCACjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,SAAS;;;;;;;;;;;;;;;;;;8BAKf,6LAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,kBACvC,6LAAC;wBAAI,WAAU;;4BAAyC;4BAC9B;4BAAO;;;;;;6CAGjC,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,UAAU,SAAS,iBACxD,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4CACtB,MAAM,cAAc,iBAAiB,OAAO,CAAC;4CAC7C,MAAM,OAAO,QAAQ,IAAI;4CAEzB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAC,2FAA2F,EACrG,gBAAgB,gBACZ,qCACA,sBACJ;gDACF,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,6LAAC;wDAAI,WAAW,CAAC,eAAe,EAC9B,gBAAgB,gBACZ,uCACA,YACJ;kEACA,cAAA,6LAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAe,QAAQ,KAAK;;;;;;0EAC3C,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,WAAW;;;;;;;;;;;;oDAGvB,QAAQ,QAAQ,KAAK,0BACpB,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;+CAxBrB,QAAQ,EAAE;;;;;wCA4BrB;;;;;;;+BAvCM;;;;;;;;;;;;;;;8BA+ClB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAGV,6LAAC;4BAAI,WAAU;;gCACZ,iBAAiB,MAAM;gCAAC;gCAAQ,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMnF;GA1PgB;;QAGC,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { useTheme } from \"next-themes\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Moon,\n  Sun,\n  Menu,\n  X,\n  Home,\n  User,\n  Briefcase,\n  Mail,\n  FileText,\n  Search\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { CommandPalette } from \"@/components/command-palette\";\nimport { cn } from \"@/lib/utils\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\", icon: Home },\n  { name: \"About\", href: \"/about\", icon: User },\n  { name: \"Projects\", href: \"/projects\", icon: Briefcase },\n  { name: \"Blog\", href: \"/blog\", icon: FileText },\n  { name: \"Contact\", href: \"/contact\", icon: Mail },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.metaKey || e.ctrlKey) && e.key === \"k\") {\n        e.preventDefault();\n        setCommandPaletteOpen(true);\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, []);\n\n  const toggleTheme = () => {\n    setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n  };\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <motion.header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        scrolled\n          ? \"bg-background/80 backdrop-blur-md border-b border-border\"\n          : \"bg-transparent\"\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <nav className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex-shrink-0\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue\">\n              AK\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-foreground/80 hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-accent\"\n                  >\n                    {item.name}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Command Palette, Theme Toggle & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setCommandPaletteOpen(true)}\n              className=\"hidden md:flex items-center space-x-2 text-muted-foreground hover:text-foreground\"\n            >\n              <Search className=\"h-4 w-4\" />\n              <span className=\"text-sm\">Search</span>\n              <kbd className=\"pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100\">\n                <span className=\"text-xs\">⌘</span>K\n              </kbd>\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={toggleTheme}\n              className=\"w-9 h-9\"\n            >\n              {theme === \"dark\" ? (\n                <Sun className=\"h-4 w-4\" />\n              ) : (\n                <Moon className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"w-9 h-9\"\n              >\n                {isOpen ? (\n                  <X className=\"h-4 w-4\" />\n                ) : (\n                  <Menu className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background/95 backdrop-blur-md rounded-lg mt-2 border border-border\">\n                {navItems.map((item) => {\n                  const Icon = item.icon;\n                  return (\n                    <motion.div\n                      key={item.name}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Link\n                        href={item.href}\n                        className=\"text-foreground/80 hover:text-foreground flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-accent\"\n                        onClick={() => setIsOpen(false)}\n                      >\n                        <Icon className=\"h-4 w-4 mr-3\" />\n                        {item.name}\n                      </Link>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n\n      <CommandPalette\n        open={commandPaletteOpen}\n        onOpenChange={setCommandPaletteOpen}\n      />\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;;;AApBA;;;;;;;;;AAsBA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,+MAAA,CAAA,YAAS;IAAC;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,qMAAA,CAAA,OAAI;IAAC;CACjD;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,WAAW;QACb;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;sDAAgB,CAAC;oBACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB,sBAAsB;oBACxB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;+BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,WACI,6DACA;QAEN,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwC;;;;;;;;;;;0CAMnE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CARP,KAAK,IAAI;;;;;;;;;;;;;;;0CAgBtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;;;;;;kDAItC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,UAAU,uBACT,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAKpB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU,CAAC;4CAC1B,WAAU;sDAET,uBACC,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAEb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1B,6LAAC,4LAAA,CAAA,kBAAe;kCACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,UAAU;;8DAEzB,6LAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAVP,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOV,6LAAC,2IAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB;GA7KgB;;QAIc,mJAAA,CAAA,WAAQ;;;KAJtB", "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { \n  Github, \n  Linkedin, \n  Twitter, \n  Mail, \n  Heart, \n  ArrowUp,\n  Code,\n  Coffee\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst footerLinks = {\n  navigation: [\n    { name: \"Home\", href: \"/\" },\n    { name: \"About\", href: \"/about\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  services: [\n    { name: \"Full Stack Development\", href: \"/#services\" },\n    { name: \"UI/UX Design\", href: \"/#services\" },\n    { name: \"Mobile Development\", href: \"/#services\" },\n    { name: \"Consulting\", href: \"/#services\" },\n  ],\n  resources: [\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Tech Stack\", href: \"/#tech-stack\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n};\n\nconst socialLinks = [\n  {\n    name: \"GitHub\",\n    href: \"https://github.com/ashishkamat\",\n    icon: Github,\n    color: \"hover:text-gray-600 dark:hover:text-gray-300\",\n  },\n  {\n    name: \"LinkedIn\",\n    href: \"https://linkedin.com/in/ashishkamat\",\n    icon: Linkedin,\n    color: \"hover:text-blue-600\",\n  },\n  {\n    name: \"Twitter\",\n    href: \"https://twitter.com/ashishkamat\",\n    icon: Twitter,\n    color: \"hover:text-blue-500\",\n  },\n  {\n    name: \"Email\",\n    href: \"mailto:<EMAIL>\",\n    icon: Mail,\n    color: \"hover:text-green-600\",\n  },\n];\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  return (\n    <footer className=\"bg-background border-t border-border\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 lg:py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n                viewport={{ once: true }}\n              >\n                <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue mb-4 inline-block\">\n                  Ashish Kamat\n                </Link>\n                <p className=\"text-muted-foreground mb-6 max-w-md\">\n                  Full Stack Developer & UI/UX Designer passionate about creating \n                  innovative digital experiences that make a difference.\n                </p>\n                \n                {/* Social Links */}\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => {\n                    const Icon = social.icon;\n                    return (\n                      <motion.a\n                        key={social.name}\n                        href={social.href}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className={`p-2 rounded-lg bg-muted hover:bg-accent transition-all duration-300 ${social.color}`}\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        initial={{ opacity: 0, scale: 0 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        transition={{ duration: 0.3, delay: index * 0.1 }}\n                        viewport={{ once: true }}\n                      >\n                        <Icon className=\"h-5 w-5\" />\n                      </motion.a>\n                    );\n                  })}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Navigation Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Navigation</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.navigation.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Services Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Services</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Resources Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <motion.div\n          className=\"py-6 border-t border-border\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n              <span>© 2024 Ashish Kamat. Made with</span>\n              <Heart className=\"h-4 w-4 text-red-500 animate-pulse\" />\n              <span>and</span>\n              <Coffee className=\"h-4 w-4 text-amber-600\" />\n              <span>in Mumbai</span>\n            </div>\n\n            {/* Tech Stack & Back to Top */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <Code className=\"h-4 w-4\" />\n                <span>Built with Next.js & Tailwind CSS</span>\n              </div>\n              \n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={scrollToTop}\n                className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n              >\n                <ArrowUp className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Fun Easter Egg */}\n        <motion.div\n          className=\"text-center py-4 border-t border-border/50\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <p className=\"text-xs text-muted-foreground/70\">\n            🚀 This website is powered by{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% coffee\n            </span>{\" \"}\n            and{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% passion\n            </span>\n          </p>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAgBA,MAAM,cAAc;IAClB,YAAY;QACV;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAA0B,MAAM;QAAa;QACrD;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAc,MAAM;QAAa;KAC1C;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAe;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA0D;;;;;;sDAGnF,6LAAC;4CAAE,WAAU;sDAAsC;;;;;;sDAMnD,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,oEAAoE,EAAE,OAAO,KAAK,EAAE;oDAChG,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,aAAa;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDACpC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;8DAEvB,cAAA,6LAAC;wDAAK,WAAU;;;;;;mDAZX,OAAO,IAAI;;;;;4CAetB;;;;;;;;;;;;;;;;;0CAMN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC3B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAK;;;;;;kDACN,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAIR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAE,WAAU;;4BAAmC;4BAChB;0CAC9B,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;4BAC3B;4BAAI;4BACR;0CACJ,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;KApLgB", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Search, Rss, BookOpen, TrendingUp } from \"lucide-react\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\n\nexport function BlogHero() {\n  return (\n    <section className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <div className=\"space-y-4\">\n              <motion.h1\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2 }}\n              >\n                My <span className=\"gradient-text-blue\">Blog</span>\n              </motion.h1>\n              \n              <motion.p\n                className=\"text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                Thoughts, tutorials, and insights on web development, React, TypeScript, \n                and the ever-evolving world of modern frontend technologies.\n              </motion.p>\n            </div>\n\n            {/* Search Bar */}\n            <motion.div\n              className=\"max-w-md mx-auto\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n            >\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  placeholder=\"Search articles...\"\n                  className=\"pl-10 pr-4 py-3 text-base\"\n                />\n              </div>\n            </motion.div>\n\n            {/* Blog Stats */}\n            <motion.div\n              className=\"grid grid-cols-3 gap-8 max-w-md mx-auto\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.5 }}\n            >\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center mx-auto mb-2\">\n                  <BookOpen className=\"h-6 w-6 text-blue-500\" />\n                </div>\n                <div className=\"text-2xl font-bold\">25+</div>\n                <div className=\"text-sm text-muted-foreground\">Articles</div>\n              </div>\n\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center mx-auto mb-2\">\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\n                </div>\n                <div className=\"text-2xl font-bold\">10K+</div>\n                <div className=\"text-sm text-muted-foreground\">Readers</div>\n              </div>\n\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center mx-auto mb-2\">\n                  <Rss className=\"h-6 w-6 text-purple-500\" />\n                </div>\n                <div className=\"text-2xl font-bold\">500+</div>\n                <div className=\"text-sm text-muted-foreground\">Subscribers</div>\n              </div>\n            </motion.div>\n\n            {/* RSS Subscribe */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 }}\n            >\n              <Button variant=\"outline\" className=\"group\">\n                <Rss className=\"mr-2 h-4 w-4 group-hover:animate-pulse\" />\n                Subscribe to RSS\n              </Button>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;;wCAC1B;sDACI,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAG1C,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAC1B;;;;;;;;;;;;sCAOH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAGjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAGjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAKnD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E;KAhGgB", "debugId": null}}, {"offset": {"line": 2242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-grid.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Calendar, Clock, ArrowRight, Tag, Eye } from \"lucide-react\";\nimport { <PERSON>, CardContent, CardHeader } from \"@/components/ui/card\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\n\nconst blogPosts = [\n  {\n    id: 1,\n    title: \"Building Scalable React Applications with TypeScript\",\n    excerpt: \"Learn how to structure large React applications using TypeScript, advanced patterns, and best practices for maintainability and performance.\",\n    content: \"In this comprehensive guide, we'll explore how to build scalable React applications using TypeScript...\",\n    slug: \"building-scalable-react-applications-typescript\",\n    category: \"React\",\n    tags: [\"React\", \"TypeScript\", \"Architecture\", \"Best Practices\"],\n    publishedAt: \"2024-01-15\",\n    readTime: \"8 min read\",\n    views: 1250,\n    featured: true,\n    author: \"Ashish Kamat\"\n  },\n  {\n    id: 2,\n    title: \"Next.js 14 App Router: Complete Guide\",\n    excerpt: \"Dive deep into Next.js 14's App Router, exploring new features, migration strategies, and performance optimizations.\",\n    content: \"Next.js 14 introduces significant improvements to the App Router...\",\n    slug: \"nextjs-14-app-router-complete-guide\",\n    category: \"Next.js\",\n    tags: [\"Next.js\", \"React\", \"App Router\", \"Performance\"],\n    publishedAt: \"2024-01-10\",\n    readTime: \"12 min read\",\n    views: 980,\n    featured: true,\n    author: \"Ashish Kamat\"\n  },\n  {\n    id: 3,\n    title: \"Mastering CSS Grid and Flexbox in 2024\",\n    excerpt: \"A comprehensive guide to modern CSS layout techniques, comparing Grid and Flexbox with practical examples and use cases.\",\n    content: \"CSS Grid and Flexbox are powerful layout systems that have revolutionized web design...\",\n    slug: \"mastering-css-grid-flexbox-2024\",\n    category: \"CSS\",\n    tags: [\"CSS\", \"Grid\", \"Flexbox\", \"Layout\"],\n    publishedAt: \"2024-01-05\",\n    readTime: \"10 min read\",\n    views: 750,\n    featured: false,\n    author: \"Ashish Kamat\"\n  },\n  {\n    id: 4,\n    title: \"State Management in React: Redux vs Zustand vs Context\",\n    excerpt: \"Compare different state management solutions for React applications and learn when to use each approach.\",\n    content: \"State management is a crucial aspect of React development...\",\n    slug: \"react-state-management-redux-zustand-context\",\n    category: \"React\",\n    tags: [\"React\", \"State Management\", \"Redux\", \"Zustand\"],\n    publishedAt: \"2023-12-28\",\n    readTime: \"15 min read\",\n    views: 1100,\n    featured: false,\n    author: \"Ashish Kamat\"\n  },\n  {\n    id: 5,\n    title: \"Building RESTful APIs with Node.js and Express\",\n    excerpt: \"Learn how to create robust and scalable REST APIs using Node.js, Express, and modern JavaScript patterns.\",\n    content: \"Building APIs is a fundamental skill for full-stack developers...\",\n    slug: \"building-restful-apis-nodejs-express\",\n    category: \"Backend\",\n    tags: [\"Node.js\", \"Express\", \"API\", \"Backend\"],\n    publishedAt: \"2023-12-20\",\n    readTime: \"11 min read\",\n    views: 890,\n    featured: false,\n    author: \"Ashish Kamat\"\n  },\n  {\n    id: 6,\n    title: \"Database Design Best Practices for Web Applications\",\n    excerpt: \"Essential database design principles, normalization techniques, and optimization strategies for modern web applications.\",\n    content: \"Good database design is the foundation of any successful web application...\",\n    slug: \"database-design-best-practices-web-applications\",\n    category: \"Database\",\n    tags: [\"Database\", \"PostgreSQL\", \"Design\", \"Optimization\"],\n    publishedAt: \"2023-12-15\",\n    readTime: \"9 min read\",\n    views: 650,\n    featured: false,\n    author: \"Ashish Kamat\"\n  }\n];\n\nconst categories = [\"All\", \"React\", \"Next.js\", \"CSS\", \"Backend\", \"Database\"];\n\nexport function BlogGrid() {\n  const [selectedCategory, setSelectedCategory] = useState(\"All\");\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const filteredPosts = selectedCategory === \"All\" \n    ? blogPosts \n    : blogPosts.filter(post => post.category === selectedCategory);\n\n  const featuredPosts = blogPosts.filter(post => post.featured);\n  const regularPosts = filteredPosts.filter(post => !post.featured);\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <section ref={ref} className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Featured Posts */}\n        {selectedCategory === \"All\" && featuredPosts.length > 0 && (\n          <div className=\"mb-16\">\n            <motion.h2\n              className=\"text-2xl font-bold mb-8\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.6 }}\n            >\n              Featured Articles\n            </motion.h2>\n            \n            <div className=\"grid lg:grid-cols-2 gap-8\">\n              {featuredPosts.map((post, index) => (\n                <motion.div\n                  key={post.id}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={inView ? { opacity: 1, y: 0 } : {}}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                >\n                  <Card className=\"h-full hover-lift group border-border/50 hover:border-border transition-all duration-300\">\n                    <div className=\"relative h-48 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-t-lg\">\n                      <div className=\"absolute inset-0 flex items-center justify-center\">\n                        <div className=\"text-6xl opacity-20\">📝</div>\n                      </div>\n                      <div className=\"absolute top-4 left-4\">\n                        <Badge className=\"bg-yellow-500 text-yellow-900\">Featured</Badge>\n                      </div>\n                      <div className=\"absolute top-4 right-4\">\n                        <Badge variant=\"secondary\">{post.category}</Badge>\n                      </div>\n                    </div>\n                    \n                    <CardHeader>\n                      <div className=\"flex items-center space-x-4 text-sm text-muted-foreground mb-2\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Calendar className=\"h-4 w-4\" />\n                          <span>{formatDate(post.publishedAt)}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-1\">\n                          <Clock className=\"h-4 w-4\" />\n                          <span>{post.readTime}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-1\">\n                          <Eye className=\"h-4 w-4\" />\n                          <span>{post.views}</span>\n                        </div>\n                      </div>\n                      <h3 className=\"text-xl font-bold group-hover:text-primary transition-colors duration-300 line-clamp-2\">\n                        {post.title}\n                      </h3>\n                    </CardHeader>\n                    \n                    <CardContent>\n                      <p className=\"text-muted-foreground mb-4 line-clamp-3\">\n                        {post.excerpt}\n                      </p>\n                      \n                      <div className=\"flex flex-wrap gap-2 mb-4\">\n                        {post.tags.slice(0, 3).map((tag) => (\n                          <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                            <Tag className=\"h-3 w-3 mr-1\" />\n                            {tag}\n                          </Badge>\n                        ))}\n                      </div>\n                      \n                      <Button asChild variant=\"ghost\" className=\"group p-0 h-auto\">\n                        <Link href={`/blog/${post.slug}`}>\n                          Read More\n                          <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                        </Link>\n                      </Button>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Category Filter */}\n        <motion.div\n          className=\"flex flex-wrap justify-center gap-2 mb-12\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          {categories.map((category) => (\n            <Button\n              key={category}\n              variant={selectedCategory === category ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={() => setSelectedCategory(category)}\n              className=\"transition-all duration-300\"\n            >\n              {category}\n            </Button>\n          ))}\n        </motion.div>\n\n        {/* All Posts */}\n        <div>\n          <motion.h2\n            className=\"text-2xl font-bold mb-8\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={inView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.3 }}\n          >\n            {selectedCategory === \"All\" ? \"All Articles\" : `${selectedCategory} Articles`}\n          </motion.h2>\n          \n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {(selectedCategory === \"All\" ? regularPosts : filteredPosts).map((post, index) => (\n              <motion.div\n                key={post.id}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={inView ? { opacity: 1, scale: 1 } : {}}\n                transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}\n                layout\n              >\n                <Card className=\"h-full hover-lift group border-border/50 hover:border-border transition-all duration-300\">\n                  <div className=\"relative h-40 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-t-lg\">\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\n                      <div className=\"text-4xl opacity-20\">📄</div>\n                    </div>\n                    <div className=\"absolute top-3 right-3\">\n                      <Badge variant=\"secondary\" className=\"text-xs\">{post.category}</Badge>\n                    </div>\n                  </div>\n                  \n                  <CardHeader className=\"pb-2\">\n                    <div className=\"flex items-center space-x-3 text-xs text-muted-foreground mb-2\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Calendar className=\"h-3 w-3\" />\n                        <span>{formatDate(post.publishedAt)}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <Clock className=\"h-3 w-3\" />\n                        <span>{post.readTime}</span>\n                      </div>\n                    </div>\n                    <h3 className=\"font-bold group-hover:text-primary transition-colors duration-300 line-clamp-2\">\n                      {post.title}\n                    </h3>\n                  </CardHeader>\n                  \n                  <CardContent>\n                    <p className=\"text-sm text-muted-foreground mb-3 line-clamp-2\">\n                      {post.excerpt}\n                    </p>\n                    \n                    <div className=\"flex flex-wrap gap-1 mb-3\">\n                      {post.tags.slice(0, 2).map((tag) => (\n                        <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                          {tag}\n                        </Badge>\n                      ))}\n                    </div>\n                    \n                    <Button asChild variant=\"ghost\" size=\"sm\" className=\"group p-0 h-auto\">\n                      <Link href={`/blog/${post.slug}`}>\n                        Read More\n                        <ArrowRight className=\"ml-1 h-3 w-3 group-hover:translate-x-1 transition-transform duration-300\" />\n                      </Link>\n                    </Button>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWA,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAS;YAAc;YAAgB;SAAiB;QAC/D,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAW;YAAS;YAAc;SAAc;QACvD,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAO;YAAQ;YAAW;SAAS;QAC1C,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAS;YAAoB;YAAS;SAAU;QACvD,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAW;YAAW;YAAO;SAAU;QAC9C,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAY;YAAc;YAAU;SAAe;QAC1D,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;IACV;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAS;IAAW;IAAO;IAAW;CAAW;AAErE,SAAS;;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,gBAAgB,qBAAqB,QACvC,YACA,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAE/C,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAC5D,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;IAEhE,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;gBAEZ,qBAAqB,SAAS,cAAc,MAAM,GAAG,mBACpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;4BAAI;sCAC7B;;;;;;sCAID,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;8CAEhD,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEAAsB;;;;;;;;;;;kEAEvC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAgC;;;;;;;;;;;kEAEnD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa,KAAK,QAAQ;;;;;;;;;;;;;;;;;0DAI7C,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;kFAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;0EAEpC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;kFAAM,KAAK,QAAQ;;;;;;;;;;;;0EAEtB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;kFACf,6LAAC;kFAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;kEAGrB,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;;;;;;;0DAIf,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAE,WAAU;kEACV,KAAK,OAAO;;;;;;kEAGf,6LAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,6LAAC,oIAAA,CAAA,QAAK;gEAAW,SAAQ;gEAAU,WAAU;;kFAC3C,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEACd;;+DAFS;;;;;;;;;;kEAOhB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,SAAQ;wDAAQ,WAAU;kEACxC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;;gEAAE;8EAEhC,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAvDzB,KAAK,EAAE;;;;;;;;;;;;;;;;8BAmEtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAEvC,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,SAAM;4BAEL,SAAS,qBAAqB,WAAW,YAAY;4BACrD,MAAK;4BACL,SAAS,IAAM,oBAAoB;4BACnC,WAAU;sCAET;2BANI;;;;;;;;;;8BAYX,6LAAC;;sCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAEvC,qBAAqB,QAAQ,iBAAiB,GAAG,iBAAiB,SAAS,CAAC;;;;;;sCAG/E,6LAAC;4BAAI,WAAU;sCACZ,CAAC,qBAAqB,QAAQ,eAAe,aAAa,EAAE,GAAG,CAAC,CAAC,MAAM,sBACtE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI,CAAC;oCAC9C,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,MAAM;8CAEN,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEAAsB;;;;;;;;;;;kEAEvC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAW,KAAK,QAAQ;;;;;;;;;;;;;;;;;0DAIjE,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;kFAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;0EAEpC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;kFAAM,KAAK,QAAQ;;;;;;;;;;;;;;;;;;kEAGxB,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;;;;;;;0DAIf,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAE,WAAU;kEACV,KAAK,OAAO;;;;;;kEAGf,6LAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,6LAAC,oIAAA,CAAA,QAAK;gEAAW,SAAQ;gEAAU,WAAU;0EAC1C;+DADS;;;;;;;;;;kEAMhB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,WAAU;kEAClD,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;;gEAAE;8EAEhC,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAhDzB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4D5B;GAxMgB;;QAEQ,sKAAA,CAAA,YAAS;;;KAFjB", "debugId": null}}, {"offset": {"line": 3101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-newsletter.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Mail, Send, CheckCircle } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { toast } from \"sonner\";\n\nexport function BlogNewsletter() {\n  const [email, setEmail] = useState(\"\");\n  const [isSubscribed, setIsSubscribed] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email) return;\n\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      setIsSubscribed(true);\n      toast.success(\"Successfully subscribed to the newsletter!\");\n      setEmail(\"\");\n    } catch (error) {\n      toast.error(\"Failed to subscribe. Please try again.\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"max-w-2xl mx-auto\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <Card className=\"border-border/50 bg-gradient-to-br from-background to-muted/30\">\n            <CardContent className=\"p-8 text-center\">\n              <motion.div\n                className=\"space-y-6\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={inView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: 0.2 }}\n              >\n                <div className=\"w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto\">\n                  <Mail className=\"h-8 w-8 text-primary\" />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <h3 className=\"text-2xl font-bold\">Stay Updated</h3>\n                  <p className=\"text-muted-foreground\">\n                    Get the latest articles, tutorials, and insights delivered straight to your inbox. \n                    No spam, unsubscribe at any time.\n                  </p>\n                </div>\n\n                {!isSubscribed ? (\n                  <form onSubmit={handleSubmit} className=\"space-y-4\">\n                    <div className=\"flex flex-col sm:flex-row gap-3\">\n                      <Input\n                        type=\"email\"\n                        placeholder=\"Enter your email address\"\n                        value={email}\n                        onChange={(e) => setEmail(e.target.value)}\n                        className=\"flex-1\"\n                        required\n                      />\n                      <Button \n                        type=\"submit\" \n                        disabled={isLoading}\n                        className=\"group\"\n                      >\n                        {isLoading ? (\n                          <>\n                            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\" />\n                            Subscribing...\n                          </>\n                        ) : (\n                          <>\n                            <Send className=\"mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                            Subscribe\n                          </>\n                        )}\n                      </Button>\n                    </div>\n                    <p className=\"text-xs text-muted-foreground\">\n                      Join 500+ developers who get weekly updates on web development trends and tutorials.\n                    </p>\n                  </form>\n                ) : (\n                  <motion.div\n                    className=\"space-y-4\"\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5 }}\n                  >\n                    <div className=\"w-16 h-16 rounded-full bg-green-500/10 flex items-center justify-center mx-auto\">\n                      <CheckCircle className=\"h-8 w-8 text-green-500\" />\n                    </div>\n                    <div>\n                      <h4 className=\"text-xl font-semibold text-green-600 dark:text-green-400\">\n                        Successfully Subscribed!\n                      </h4>\n                      <p className=\"text-muted-foreground mt-2\">\n                        Thank you for subscribing! You'll receive a confirmation email shortly.\n                      </p>\n                    </div>\n                  </motion.div>\n                )}\n\n                <div className=\"grid grid-cols-3 gap-6 pt-6 border-t border-border/50\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-primary\">500+</div>\n                    <div className=\"text-sm text-muted-foreground\">Subscribers</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-primary\">25+</div>\n                    <div className=\"text-sm text-muted-foreground\">Articles</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-primary\">Weekly</div>\n                    <div className=\"text-sm text-muted-foreground\">Updates</div>\n                  </div>\n                </div>\n              </motion.div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,OAAO;QAEZ,aAAa;QAEb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,gBAAgB;YAChB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS;QACX,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE,IAAI,CAAC;gBAC1C,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;gCAMtC,CAAC,6BACA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,QAAQ;;;;;;8DAEV,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,0BACC;;0EACE,6LAAC;gEAAI,WAAU;;;;;;4DAAmE;;qFAIpF;;0EACE,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAA6E;;;;;;;;;;;;;;sDAMrG,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;yDAK/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2D;;;;;;8DAGzE,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAOhD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnE;GArIgB;;QAIQ,sKAAA,CAAA,YAAS;;;KAJjB", "debugId": null}}]}