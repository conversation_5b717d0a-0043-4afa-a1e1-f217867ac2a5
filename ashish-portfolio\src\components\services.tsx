"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { 
  Code, 
  Palette, 
  Smartphone, 
  Database, 
  Cloud, 
  Zap,
  ArrowRight
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const services = [
  {
    icon: Code,
    title: "Full Stack Development",
    description: "End-to-end web application development using modern technologies like React, Next.js, Node.js, and TypeScript.",
    features: ["Custom Web Applications", "API Development", "Database Design", "Performance Optimization"],
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
  },
  {
    icon: Palette,
    title: "UI/UX Design",
    description: "Creating beautiful, intuitive user interfaces and experiences that delight users and drive engagement.",
    features: ["User Interface Design", "User Experience Research", "Prototyping", "Design Systems"],
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
  },
  {
    icon: Smartphone,
    title: "Mobile Development",
    description: "Building responsive web applications and mobile-first experiences that work seamlessly across all devices.",
    features: ["Responsive Design", "Progressive Web Apps", "Mobile Optimization", "Cross-platform Solutions"],
    color: "text-green-500",
    bgColor: "bg-green-500/10",
  },
  {
    icon: Database,
    title: "Backend Solutions",
    description: "Robust backend systems, APIs, and database architectures that scale with your business needs.",
    features: ["RESTful APIs", "Database Architecture", "Authentication Systems", "Third-party Integrations"],
    color: "text-orange-500",
    bgColor: "bg-orange-500/10",
  },
  {
    icon: Cloud,
    title: "Cloud & DevOps",
    description: "Deployment, hosting, and DevOps solutions to ensure your applications are fast, secure, and reliable.",
    features: ["Cloud Deployment", "CI/CD Pipelines", "Performance Monitoring", "Security Implementation"],
    color: "text-cyan-500",
    bgColor: "bg-cyan-500/10",
  },
  {
    icon: Zap,
    title: "Performance Optimization",
    description: "Making your applications lightning-fast with advanced optimization techniques and best practices.",
    features: ["Speed Optimization", "SEO Enhancement", "Code Splitting", "Caching Strategies"],
    color: "text-yellow-500",
    bgColor: "bg-yellow-500/10",
  },
];

export function Services() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section ref={ref} className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Services I Offer</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            From concept to deployment, I provide comprehensive development services 
            to bring your digital vision to life.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 50 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.1 }}
              >
                <Card className="h-full hover-lift group cursor-pointer border-border/50 hover:border-border transition-all duration-300">
                  <CardHeader>
                    <div className={`w-12 h-12 rounded-lg ${service.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className={`h-6 w-6 ${service.color}`} />
                    </div>
                    <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors duration-300">
                      {service.title}
                    </CardTitle>
                    <CardDescription className="text-muted-foreground">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 mb-6">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-muted-foreground">
                          <div className={`w-1.5 h-1.5 rounded-full ${service.color.replace('text-', 'bg-')} mr-3`} />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button 
                      variant="ghost" 
                      className="w-full group-hover:bg-accent transition-colors duration-300"
                    >
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* CTA Section */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-border/50">
            <h3 className="text-2xl font-bold mb-4">Ready to Start Your Project?</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Let's discuss how I can help bring your ideas to life with modern, 
              scalable, and user-friendly solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="group">
                Get Started
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
              </Button>
              <Button variant="outline" size="lg">
                View Portfolio
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
